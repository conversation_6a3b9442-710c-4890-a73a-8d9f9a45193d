import { defineConfig } from '@adonisjs/core/app'
import edge from 'edge.js'

/**
 * Configuration for the Edge template engine
 */
export default defineConfig({
  /**
   * Path to the views directory
   */
  viewsPath: 'resources/views',

  /**
   * Cache compiled templates in production
   */
  cache: process.env.NODE_ENV === 'production',
})

/**
 * Configure Edge.js instance
 */
edge.mount('default', 'resources/views')

// Configure Edge for development
if (process.env.NODE_ENV !== 'production') {
  edge.configure({
    cache: false,
  })
}

export { edge }
