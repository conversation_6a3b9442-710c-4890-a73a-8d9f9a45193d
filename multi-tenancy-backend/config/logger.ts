import env from '#start/env'
import app from '@adonisjs/core/services/app'
import { defineConfig, targets } from '@adonisjs/core/logger'

const loggerConfig = defineConfig({
  default: 'app',

  /**
   * The loggers object can be used to define multiple loggers.
   * By default, we configure only one logger (named "app").
   */
  loggers: {
    app: {
      enabled: true,
      name: env.get('APP_NAME'),
      level: env.get('LOG_LEVEL'),
      transport: {
        targets: targets()
          .pushIf(!app.inProduction, targets.pretty())
          .pushIf(app.inProduction, targets.file({ destination: 1 }))
          // Add file logging for all environments
          .push({
            target: 'pino/file',
            level: 'debug',
            options: {
              destination: './logs/app.log',
              mkdir: true,
            },
          })
          .toArray(),
      },
      // Redact sensitive information from logs
      redact: {
        paths: [
          'password',
          '*.password',
          'stripe_signature',
          'access_token',
          'accessToken',
          'client_secret',
          'clientSecret',
          'api_key',
          'apiKey',
          'authorization',
          'cookie',
          '["set-cookie"]', // Properly quoted for hyphenated header name
          '["stripe-signature"]', // Stripe webhook signature header
          '["x-api-key"]', // API key headers
          'token',
          'refresh_token',
          'refreshToken',
        ],
        censor: '[REDACTED]',
      },
    },
  },
})

export default loggerConfig

/**
 * Inferring types for the list of loggers you have configured
 * in your application.
 */
declare module '@adonisjs/core/types' {
  export interface LoggersList extends InferLoggers<typeof loggerConfig> {}
}
