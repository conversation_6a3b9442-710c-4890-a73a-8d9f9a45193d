#!/usr/bin/env node

/**
 * Complete database setup script
 * Runs migrations and seeders in the correct order
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Multi-Tenant SaaS Database Setup');
console.log('====================================');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  Warning: .env file not found!');
  console.log('   Please create a .env file with your database configuration.');
  console.log('   See README.md for example configuration.');
  console.log('');
}

try {
  // Change to the project directory
  process.chdir(__dirname);
  
  console.log('📍 Current directory:', process.cwd());
  console.log('');
  
  // Step 1: Run migrations
  console.log('🗄️  Step 1: Running database migrations...');
  console.log('   Command: node ace migration:run');
  
  const migrationOutput = execSync('node ace migration:run', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('✅ Migrations completed successfully!');
  if (migrationOutput.trim()) {
    console.log('   Output:', migrationOutput.trim());
  }
  console.log('');
  
  // Step 2: Run seeders
  console.log('🌱 Step 2: Running database seeders...');
  console.log('   Command: node ace db:seed');
  
  const seederOutput = execSync('node ace db:seed', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('✅ Seeders completed successfully!');
  if (seederOutput.trim()) {
    console.log('   Output:', seederOutput.trim());
  }
  console.log('');
  
  // Success message
  console.log('🎉 Database setup completed successfully!');
  console.log('');
  console.log('👤 Default accounts created:');
  console.log('   🔑 Super Admin: <EMAIL> / SuperAdmin123!');
  console.log('   👑 Tenant Owner: <EMAIL> / DemoOwner123!');
  console.log('   👤 Tenant Admin: <EMAIL> / DemoAdmin123!');
  console.log('');
  console.log('🚀 Next steps:');
  console.log('   1. Start the server: npm run dev');
  console.log('   2. Test the API: node test-auth-api.js');
  console.log('   3. Login as super admin to manage merchants');
  
} catch (error) {
  console.error('\n❌ Database setup failed!');
  console.error('Error:', error.message);
  
  if (error.stdout) {
    console.log('\n📤 stdout:', error.stdout.toString());
  }
  
  if (error.stderr) {
    console.error('\n📥 stderr:', error.stderr.toString());
  }
  
  console.log('\n🔧 Troubleshooting:');
  console.log('   1. Ensure Node.js and npm are installed');
  console.log('   2. Install dependencies: npm install');
  console.log('   3. Check your .env file has correct database configuration');
  console.log('   4. Ensure your database server is running');
  console.log('   5. Verify database connection settings');
  
  console.log('\n📖 For more help, see README.md');
  
  process.exit(1);
}
