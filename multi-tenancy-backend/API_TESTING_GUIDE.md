# Multi-Tenant SaaS API Testing Guide

This guide provides comprehensive examples for testing the merchant CRUD operations and user authentication in the multi-tenant SaaS backend.

## 🚀 Quick Start

### Prerequisites
1. Application running on `http://localhost:3000`
2. MongoDB connection configured
3. Valid tenant domain/subdomain setup

### Base URL Structure
- **Local Development**: `http://localhost:3000/api/v1`
- **With Subdomain**: `http://tenant-name.localhost:3000/api/v1`
- **Production**: `https://tenant-subdomain.yourdomain.com/api/v1`

## 🔐 Authentication Flow

### 1. User Registration
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -H "Host: demo-tenant.localhost" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "phone": "+1234567890",
    "role": "tenant_owner"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "tenant_owner",
      "tenantId": "64f8a1b2c3d4e5f6a7b8c9d1"
    },
    "expiresIn": "24h"
  },
  "message": "Registration successful"
}
```

### 2. User Login
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Host: demo-tenant.localhost" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### 3. Get User Profile
```bash
curl -X GET http://localhost:3000/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"
```

## 🏢 Merchant CRUD Operations

### 1. Create Merchant
```bash
curl -X POST http://localhost:3000/api/v1/merchants \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost" \
  -d '{
    "businessName": "Joe'\''s Coffee Shop",
    "businessDescription": "Premium coffee and pastries",
    "businessType": "restaurant",
    "businessRegistrationNumber": "REG123456789",
    "taxId": "TAX987654321",
    "contactInfo": {
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "website": "https://joescoffee.com",
      "socialMedia": {
        "facebook": "joescoffeeshop",
        "instagram": "@joescoffee"
      }
    },
    "address": {
      "street": "123 Main Street",
      "city": "New York",
      "state": "NY",
      "country": "USA",
      "postalCode": "10001",
      "coordinates": {
        "latitude": 40.7128,
        "longitude": -74.0060
      }
    },
    "businessHours": {
      "monday": { "open": "07:00", "close": "19:00" },
      "tuesday": { "open": "07:00", "close": "19:00" },
      "wednesday": { "open": "07:00", "close": "19:00" },
      "thursday": { "open": "07:00", "close": "19:00" },
      "friday": { "open": "07:00", "close": "20:00" },
      "saturday": { "open": "08:00", "close": "20:00" },
      "sunday": { "open": "08:00", "close": "18:00" }
    },
    "paymentSettings": {
      "acceptsCash": true,
      "acceptsCards": true,
      "acceptsDigitalPayments": true,
      "currency": "USD",
      "taxRate": 8.25
    },
    "branding": {
      "primaryColor": "#8B4513",
      "secondaryColor": "#D2691E",
      "logo": "https://example.com/logo.png"
    },
    "tags": ["coffee", "breakfast", "wifi", "organic"]
  }'
```

### 2. Get All Merchants (with filtering and pagination)
```bash
# Basic list
curl -X GET http://localhost:3000/api/v1/merchants \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"

# With filters and pagination
curl -X GET "http://localhost:3000/api/v1/merchants?page=1&limit=5&search=coffee&status=active&businessType=restaurant" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"
```

### 3. Get Merchant by ID
```bash
curl -X GET http://localhost:3000/api/v1/merchants/MERCHANT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"
```

### 4. Update Merchant
```bash
curl -X PATCH http://localhost:3000/api/v1/merchants/MERCHANT_ID \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost" \
  -d '{
    "businessDescription": "Premium coffee, pastries, and light meals",
    "contactInfo": {
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "website": "https://joescoffee.com"
    },
    "businessHours": {
      "sunday": { "open": "09:00", "close": "17:00" }
    }
  }'
```

### 5. Update Merchant Status
```bash
curl -X PATCH http://localhost:3000/api/v1/merchants/MERCHANT_ID/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost" \
  -d '{
    "status": "active"
  }'
```

### 6. Get Merchant Analytics
```bash
curl -X GET http://localhost:3000/api/v1/merchants/MERCHANT_ID/analytics \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"
```

### 7. Delete Merchant
```bash
curl -X DELETE http://localhost:3000/api/v1/merchants/MERCHANT_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Host: demo-tenant.localhost"
```

## 🧪 Testing Scenarios

### Scenario 1: Complete Merchant Management Flow
1. Register a new user
2. Login to get JWT token
3. Create a merchant
4. Update merchant details
5. Get merchant analytics
6. Update merchant status

### Scenario 2: Multi-User Access Control
1. Create tenant owner
2. Create tenant admin
3. Create manager
4. Test access permissions for each role

### Scenario 3: Error Handling
1. Test invalid authentication
2. Test unauthorized access
3. Test invalid data validation
4. Test non-existent resource access

## 📊 Response Formats

### Success Response
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Operation successful",
  "pagination": { /* pagination info for lists */ }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Detailed error message",
  "message": "User-friendly error message"
}
```

## 🔧 Environment Variables for Testing

Create a `.env` file for testing:
```env
MONGODB_URI=mongodb://localhost:27017/multi-tenant-test
JWT_SECRET=test-secret-key-change-in-production
JWT_EXPIRES_IN=24h
PORT=3000
NODE_ENV=development
BASE_DOMAIN=localhost
ALLOWED_ORIGINS=http://localhost:3000,http://demo-tenant.localhost:3000
```

## 🚀 Postman Collection

Import this collection for easier testing:

```json
{
  "info": {
    "name": "Multi-Tenant SaaS API",
    "description": "Complete API collection for testing"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3000/api/v1"
    },
    {
      "key": "token",
      "value": ""
    }
  ]
}
```

## 📝 Notes

1. **Host Header**: Always include the Host header to simulate subdomain/domain routing
2. **JWT Token**: Store the token from login response and use in subsequent requests
3. **Tenant Context**: All operations are scoped to the tenant identified by the Host header
4. **Role Permissions**: Different user roles have different access levels
5. **Data Validation**: All inputs are validated according to the DTO schemas
