# Stripe Pricing Setup Scripts

This directory contains scripts to set up your Stripe pricing plans that match your application's billing structure.

## Quick Setup

### Option 1: Simple Node.js Script (Recommended)

```bash
# Navigate to backend directory
cd multi-tenancy-backend

# Run the setup script with your Stripe secret key
STRIPE_SECRET_KEY=sk_test_your_secret_key_here node scripts/setup-stripe.js
```

### Option 2: Using npm script

```bash
# Make sure your .env file has STRIPE_SECRET_KEY set
npm run setup-stripe-pricing
```

## What the Script Does

The script will create the following in your Stripe account:

### Products Created:
- **Basic Plan** - $29/month or $290/year
- **Pro Plan** - $99/month or $990/year  
- **Enterprise Plan** - $299/month or $2990/year

### Price IDs Generated:
- `price_basic_monthly`
- `price_basic_yearly`
- `price_pro_monthly`
- `price_pro_yearly`
- `price_enterprise_monthly`
- `price_enterprise_yearly`

## After Running the Script

### 1. Update Your Database Seeder

Copy the generated price IDs and update your `pricing_plan_seeder.ts`:

```typescript
// Example output from script:
stripePriceIdMonthly: 'price_1234567890abcdef', // Replace with actual ID
stripePriceIdYearly: 'price_0987654321fedcba',   // Replace with actual ID
```

### 2. Set Up Webhooks

In your Stripe Dashboard:
1. Go to **Webhooks** section
2. Add endpoint: `https://your-domain.com/api/v1/admin/stripe/webhook`
3. Select these events:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

### 3. Update Environment Variables

Make sure your `.env` files have:

```bash
# Backend (.env)
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Frontend (hardcoded in StripeProvider.tsx)
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
```

### 4. Test Your Setup

1. Run your application
2. Try subscribing to a plan
3. Check Stripe Dashboard for successful payments
4. Verify billing records are created in your database

## Troubleshooting

### "No such price" Error
- Make sure you've run the setup script
- Verify the price IDs in your database match Stripe
- Check that your Stripe keys are correct

### Webhook Issues
- Verify webhook endpoint URL is correct
- Check webhook secret in environment variables
- Test webhook delivery in Stripe Dashboard

### Payment Failures
- Use Stripe test cards: https://stripe.com/docs/testing#cards
- Check browser console for errors
- Verify Stripe publishable key is set correctly

## Test Cards

Use these test cards for development:

- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient Funds**: `****************`

## Support

- Stripe Documentation: https://stripe.com/docs
- Stripe Dashboard: https://dashboard.stripe.com
- Test Cards: https://stripe.com/docs/testing#cards
