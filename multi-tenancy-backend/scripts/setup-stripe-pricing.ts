#!/usr/bin/env node

/**
 * Stripe Pricing Setup Script
 * 
 * This script creates products and prices in Stripe that match your application's pricing plans.
 * Run this script once to set up your Stripe pricing structure.
 * 
 * Usage:
 * npm run setup-stripe-pricing
 * 
 * Make sure to set your STRIPE_SECRET_KEY environment variable before running.
 */

import Stripe from 'stripe'
import env from '#start/env'

// Initialize Stripe
const stripe = new Stripe(env.get('STRIPE_SECRET_KEY'), {
  apiVersion: '2024-12-18.acacia',
})

// Pricing plan definitions that match your database seeder
const pricingPlans = [
  {
    name: 'Free',
    slug: 'free',
    description: 'Perfect for getting started with basic features',
    monthlyPrice: 0, // $0
    yearlyPrice: 0, // $0
    currency: 'usd',
    features: [
      'Basic Support',
      'Core Features', 
      'Mobile App',
      '3 Users',
      '1GB Storage',
      '1 Project'
    ]
  },
  {
    name: 'Basic',
    slug: 'basic',
    description: 'Great for small teams and growing businesses',
    monthlyPrice: 2900, // $29.00
    yearlyPrice: 29000, // $290.00 (save $58)
    currency: 'usd',
    features: [
      'Everything in Free',
      'Priority Email Support',
      'Basic Analytics',
      '10 Users',
      '10GB Storage',
      '1,000 API Calls/month',
      '5 Projects'
    ]
  },
  {
    name: 'Pro',
    slug: 'pro',
    description: 'Perfect for professional teams and advanced workflows',
    monthlyPrice: 9900, // $99.00
    yearlyPrice: 99000, // $990.00 (save $198)
    currency: 'usd',
    features: [
      'Everything in Basic',
      'Priority Support (12hr response)',
      'Advanced Analytics',
      'API Access (10,000 calls/month)',
      'Custom Integrations',
      'Webhooks',
      '50 Users',
      '100GB Storage',
      '25 Projects'
    ]
  },
  {
    name: 'Enterprise',
    slug: 'enterprise',
    description: 'For large organizations with advanced security and compliance needs',
    monthlyPrice: 29900, // $299.00
    yearlyPrice: 299000, // $2990.00 (save $598)
    currency: 'usd',
    features: [
      'Everything in Pro',
      'Dedicated Account Manager',
      'Phone Support',
      'Unlimited API Access',
      'Custom Branding',
      'SSO Integration',
      'Advanced Security',
      'Custom Workflows',
      'Data Export',
      'Unlimited Users & Storage'
    ]
  }
]

async function createStripeProduct(plan: typeof pricingPlans[0]) {
  try {
    console.log(`\n🔄 Creating product: ${plan.name}`)
    
    // Create product
    const product = await stripe.products.create({
      name: plan.name,
      description: plan.description,
      metadata: {
        slug: plan.slug,
        features: JSON.stringify(plan.features)
      }
    })
    
    console.log(`✅ Created product: ${product.name} (${product.id})`)
    
    const prices = []
    
    // Create monthly price (skip for free plan)
    if (plan.monthlyPrice > 0) {
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.monthlyPrice,
        currency: plan.currency,
        recurring: {
          interval: 'month'
        },
        nickname: `${plan.name} Monthly`,
        metadata: {
          plan_slug: plan.slug,
          billing_cycle: 'monthly'
        }
      })
      
      prices.push({
        cycle: 'monthly',
        priceId: monthlyPrice.id,
        amount: plan.monthlyPrice
      })
      
      console.log(`  ✅ Created monthly price: ${monthlyPrice.id} ($${plan.monthlyPrice / 100}/month)`)
    }
    
    // Create yearly price (skip for free plan)
    if (plan.yearlyPrice > 0) {
      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.yearlyPrice,
        currency: plan.currency,
        recurring: {
          interval: 'year'
        },
        nickname: `${plan.name} Yearly`,
        metadata: {
          plan_slug: plan.slug,
          billing_cycle: 'yearly'
        }
      })
      
      prices.push({
        cycle: 'yearly',
        priceId: yearlyPrice.id,
        amount: plan.yearlyPrice
      })
      
      const monthlySavings = (plan.monthlyPrice * 12) - plan.yearlyPrice
      console.log(`  ✅ Created yearly price: ${yearlyPrice.id} ($${plan.yearlyPrice / 100}/year, save $${monthlySavings / 100})`)
    }
    
    return {
      product,
      prices
    }
    
  } catch (error) {
    console.error(`❌ Error creating product ${plan.name}:`, error.message)
    throw error
  }
}

async function setupStripePricing() {
  console.log('🚀 Setting up Stripe pricing plans...\n')
  
  try {
    // Verify Stripe connection
    const account = await stripe.accounts.retrieve()
    console.log(`✅ Connected to Stripe account: ${account.display_name || account.id}`)
    
    const results = []
    
    // Create each pricing plan
    for (const plan of pricingPlans) {
      const result = await createStripeProduct(plan)
      results.push({
        plan: plan.slug,
        productId: result.product.id,
        prices: result.prices
      })
    }
    
    console.log('\n🎉 Stripe pricing setup completed successfully!')
    console.log('\n📋 Summary:')
    console.log('=' * 50)
    
    results.forEach(result => {
      console.log(`\n${result.plan.toUpperCase()}:`)
      console.log(`  Product ID: ${result.productId}`)
      result.prices.forEach(price => {
        console.log(`  ${price.cycle}: ${price.priceId} ($${price.amount / 100})`)
      })
    })
    
    console.log('\n📝 Next Steps:')
    console.log('1. Update your .env file with the price IDs above')
    console.log('2. Update your pricing plan seeder with the correct Stripe price IDs')
    console.log('3. Set up webhooks in Stripe Dashboard pointing to your webhook endpoint')
    console.log('4. Test payments in your application')
    
    console.log('\n🔗 Useful Stripe Dashboard Links:')
    console.log('- Products: https://dashboard.stripe.com/products')
    console.log('- Webhooks: https://dashboard.stripe.com/webhooks')
    console.log('- Test Cards: https://stripe.com/docs/testing#cards')
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Run the setup
if (require.main === module) {
  setupStripePricing()
    .then(() => {
      console.log('\n✅ Setup completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n❌ Setup failed:', error)
      process.exit(1)
    })
}

export { setupStripePricing, pricingPlans }
