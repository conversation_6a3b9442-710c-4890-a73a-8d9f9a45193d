#!/usr/bin/env node

/**
 * Simple Stripe Pricing Setup Script
 * 
 * This script creates products and prices in Stripe that match your application's pricing plans.
 * 
 * Usage:
 * STRIPE_SECRET_KEY=sk_test_... node scripts/setup-stripe.js
 */

const Stripe = require('stripe');

// Get Stripe secret key from environment
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY;

if (!STRIPE_SECRET_KEY) {
  console.error('❌ Error: STRIPE_SECRET_KEY environment variable is required');
  console.log('Usage: STRIPE_SECRET_KEY=sk_test_... node scripts/setup-stripe.js');
  process.exit(1);
}

// Initialize Stripe
const stripe = new Stripe(STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
});

// Pricing plan definitions
const pricingPlans = [
  {
    name: 'Basic',
    slug: 'basic',
    description: 'Great for small teams and growing businesses',
    monthlyPrice: 2900, // $29.00
    yearlyPrice: 29000, // $290.00
    currency: 'usd',
  },
  {
    name: 'Pro',
    slug: 'pro',
    description: 'Perfect for professional teams and advanced workflows',
    monthlyPrice: 9900, // $99.00
    yearlyPrice: 99000, // $990.00
    currency: 'usd',
  },
  {
    name: 'Enterprise',
    slug: 'enterprise',
    description: 'For large organizations with advanced security and compliance needs',
    monthlyPrice: 29900, // $299.00
    yearlyPrice: 299000, // $2990.00
    currency: 'usd',
  }
];

async function createStripeProduct(plan) {
  try {
    console.log(`\n🔄 Creating product: ${plan.name}`);
    
    // Create product
    const product = await stripe.products.create({
      name: plan.name,
      description: plan.description,
      metadata: {
        slug: plan.slug,
      }
    });
    
    console.log(`✅ Created product: ${product.name} (${product.id})`);
    
    const prices = [];
    
    // Create monthly price
    const monthlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.monthlyPrice,
      currency: plan.currency,
      recurring: {
        interval: 'month'
      },
      nickname: `${plan.name} Monthly`,
      metadata: {
        plan_slug: plan.slug,
        billing_cycle: 'monthly'
      }
    });
    
    prices.push({
      cycle: 'monthly',
      priceId: monthlyPrice.id,
      amount: plan.monthlyPrice
    });
    
    console.log(`  ✅ Monthly: ${monthlyPrice.id} ($${plan.monthlyPrice / 100}/month)`);
    
    // Create yearly price
    const yearlyPrice = await stripe.prices.create({
      product: product.id,
      unit_amount: plan.yearlyPrice,
      currency: plan.currency,
      recurring: {
        interval: 'year'
      },
      nickname: `${plan.name} Yearly`,
      metadata: {
        plan_slug: plan.slug,
        billing_cycle: 'yearly'
      }
    });
    
    prices.push({
      cycle: 'yearly',
      priceId: yearlyPrice.id,
      amount: plan.yearlyPrice
    });
    
    const monthlySavings = (plan.monthlyPrice * 12) - plan.yearlyPrice;
    console.log(`  ✅ Yearly: ${yearlyPrice.id} ($${plan.yearlyPrice / 100}/year, save $${monthlySavings / 100})`);
    
    return {
      product,
      prices
    };
    
  } catch (error) {
    console.error(`❌ Error creating product ${plan.name}:`, error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Setting up Stripe pricing plans...\n');
  
  try {
    // Verify Stripe connection
    const account = await stripe.accounts.retrieve();
    console.log(`✅ Connected to Stripe account: ${account.display_name || account.id}`);
    
    const results = [];
    
    // Create each pricing plan
    for (const plan of pricingPlans) {
      const result = await createStripeProduct(plan);
      results.push({
        plan: plan.slug,
        productId: result.product.id,
        prices: result.prices
      });
    }
    
    console.log('\n🎉 Stripe pricing setup completed successfully!');
    console.log('\n📋 Price IDs for your application:');
    console.log('=' * 50);
    
    results.forEach(result => {
      result.prices.forEach(price => {
        const priceIdName = `price_${result.plan}_${price.cycle}`;
        console.log(`${priceIdName}: ${price.priceId}`);
      });
    });
    
    console.log('\n📝 Next Steps:');
    console.log('1. Copy the price IDs above');
    console.log('2. Update your pricing plan seeder with these Stripe price IDs');
    console.log('3. Set up webhooks in Stripe Dashboard');
    console.log('4. Test payments in your application');
    
    console.log('\n🔗 Stripe Dashboard: https://dashboard.stripe.com/products');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
main()
  .then(() => {
    console.log('\n✅ Setup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Setup failed:', error);
    process.exit(1);
  });
