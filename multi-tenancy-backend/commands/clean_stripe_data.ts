import { BaseCommand, flags } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import Stripe from 'stripe'
import env from '#start/env'

export default class CleanStripeData extends BaseCommand {
  static commandName = 'clean:stripe-data'
  static description = 'Clean all Stripe sandbox data (customers, subscriptions, prices, products)'

  static options: CommandOptions = {
    startApp: true,
  }

  @flags.boolean({ description: 'Skip confirmation prompt' })
  declare force: boolean

  @flags.boolean({ description: 'Clean customers only' })
  declare customersOnly: boolean

  @flags.boolean({ description: 'Clean subscriptions only' })
  declare subscriptionsOnly: boolean

  @flags.boolean({ description: 'Clean prices only' })
  declare pricesOnly: boolean

  @flags.boolean({ description: 'Clean products only' })
  declare productsOnly: boolean

  @flags.boolean({ description: 'Dry run - show what would be deleted without actually deleting' })
  declare dryRun: boolean

  private stripe!: Stripe

  async run() {
    // Initialize Stripe
    const stripeSecretKey = env.get('STRIPE_SECRET_KEY')
    if (!stripeSecretKey) {
      this.logger.error('❌ STRIPE_SECRET_KEY not found in environment variables')
      return
    }

    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-02-24.acacia',
    })

    // Check if we're in test mode
    if (!stripeSecretKey.startsWith('sk_test_')) {
      this.logger.error('❌ This command only works with test/sandbox Stripe keys (sk_test_)')
      this.logger.error('   Current key starts with: ' + stripeSecretKey.substring(0, 8) + '...')
      return
    }

    this.logger.info('🧹 Stripe Sandbox Data Cleaner')
    this.logger.info('===============================')
    this.logger.info(`🔑 Using Stripe key: ${stripeSecretKey.substring(0, 12)}...`)

    if (!this.force && !this.dryRun) {
      const confirmed = await this.prompt.confirm(
        '⚠️  This will permanently delete ALL Stripe sandbox data. Are you sure?'
      )
      if (!confirmed) {
        this.logger.info('❌ Operation cancelled')
        return
      }
    }

    if (this.dryRun) {
      this.logger.info('🔍 DRY RUN MODE - No data will be actually deleted')
    }

    try {
      // Determine what to clean based on flags
      const cleanAll =
        !this.customersOnly && !this.subscriptionsOnly && !this.pricesOnly && !this.productsOnly

      if (cleanAll || this.subscriptionsOnly) {
        await this.cleanSubscriptions()
      }

      if (cleanAll || this.customersOnly) {
        await this.cleanCustomers()
      }

      if (cleanAll || this.pricesOnly) {
        await this.cleanPrices()
      }

      if (cleanAll || this.productsOnly) {
        await this.cleanProducts()
      }

      this.logger.success('✅ Stripe sandbox cleanup completed!')
    } catch (error: any) {
      this.logger.error('❌ Error during cleanup:', error.message)
      if (error.stack) {
        this.logger.error(error.stack)
      }
    }
  }

  private async cleanSubscriptions() {
    this.logger.info('🔄 Cleaning subscriptions...')

    try {
      const subscriptions = await this.stripe.subscriptions.list({
        limit: 100,
        status: 'all',
      })

      if (subscriptions.data.length === 0) {
        this.logger.info('   ℹ️  No subscriptions found')
        return
      }

      this.logger.info(`   📋 Found ${subscriptions.data.length} subscriptions`)

      for (const subscription of subscriptions.data) {
        if (this.dryRun) {
          this.logger.info(
            `   🔍 Would delete subscription: ${subscription.id} (${subscription.status})`
          )
        } else {
          try {
            if (subscription.status === 'active' || subscription.status === 'trialing') {
              await this.stripe.subscriptions.cancel(subscription.id)
              this.logger.info(`   ✅ Cancelled subscription: ${subscription.id}`)
            } else {
              this.logger.info(
                `   ⚠️  Subscription ${subscription.id} already ${subscription.status}`
              )
            }
          } catch (error: any) {
            this.logger.error(
              `   ❌ Failed to cancel subscription ${subscription.id}: ${error.message}`
            )
          }
        }
      }

      if (!this.dryRun) {
        this.logger.success(`   ✅ Processed ${subscriptions.data.length} subscriptions`)
      }
    } catch (error: any) {
      this.logger.error(`❌ Error cleaning subscriptions: ${error.message}`)
    }
  }

  private async cleanCustomers() {
    this.logger.info('🔄 Cleaning customers...')

    try {
      let hasMore = true
      let startingAfter: string | undefined
      let totalDeleted = 0

      while (hasMore) {
        const customers = await this.stripe.customers.list({
          limit: 100,
          starting_after: startingAfter,
        })

        if (customers.data.length === 0) {
          break
        }

        this.logger.info(`   📋 Found ${customers.data.length} customers in this batch`)

        for (const customer of customers.data) {
          if (this.dryRun) {
            this.logger.info(
              `   🔍 Would delete customer: ${customer.id} (${customer.email || 'no email'})`
            )
          } else {
            try {
              await this.stripe.customers.del(customer.id)
              this.logger.info(
                `   ✅ Deleted customer: ${customer.id} (${customer.email || 'no email'})`
              )
              totalDeleted++
            } catch (error: any) {
              this.logger.error(`   ❌ Failed to delete customer ${customer.id}: ${error.message}`)
            }
          }
        }

        hasMore = customers.has_more
        if (hasMore && customers.data.length > 0) {
          startingAfter = customers.data[customers.data.length - 1].id
        }
      }

      if (!this.dryRun) {
        this.logger.success(`   ✅ Deleted ${totalDeleted} customers`)
      }
    } catch (error: any) {
      this.logger.error(`❌ Error cleaning customers: ${error.message}`)
    }
  }

  private async cleanPrices() {
    this.logger.info('🔄 Cleaning prices...')

    try {
      let hasMore = true
      let startingAfter: string | undefined
      let totalArchived = 0

      while (hasMore) {
        const prices = await this.stripe.prices.list({
          limit: 100,
          starting_after: startingAfter,
        })

        if (prices.data.length === 0) {
          break
        }

        this.logger.info(`   📋 Found ${prices.data.length} prices in this batch`)

        for (const price of prices.data) {
          if (this.dryRun) {
            this.logger.info(
              `   🔍 Would archive price: ${price.id} (${price.nickname || 'no nickname'})`
            )
          } else {
            try {
              if (price.active) {
                await this.stripe.prices.update(price.id, { active: false })
                this.logger.info(
                  `   ✅ Archived price: ${price.id} (${price.nickname || 'no nickname'})`
                )
                totalArchived++
              } else {
                this.logger.info(`   ⚠️  Price ${price.id} already archived`)
              }
            } catch (error: any) {
              this.logger.error(`   ❌ Failed to archive price ${price.id}: ${error.message}`)
            }
          }
        }

        hasMore = prices.has_more
        if (hasMore && prices.data.length > 0) {
          startingAfter = prices.data[prices.data.length - 1].id
        }
      }

      if (!this.dryRun) {
        this.logger.success(`   ✅ Archived ${totalArchived} prices`)
      }
    } catch (error: any) {
      this.logger.error(`❌ Error cleaning prices: ${error.message}`)
    }
  }

  private async cleanProducts() {
    this.logger.info('🔄 Cleaning products...')

    try {
      let hasMore = true
      let startingAfter: string | undefined
      let totalArchived = 0

      while (hasMore) {
        const products = await this.stripe.products.list({
          limit: 100,
          starting_after: startingAfter,
        })

        if (products.data.length === 0) {
          break
        }

        this.logger.info(`   📋 Found ${products.data.length} products in this batch`)

        for (const product of products.data) {
          if (this.dryRun) {
            this.logger.info(`   🔍 Would archive product: ${product.id} (${product.name})`)
          } else {
            try {
              if (product.active) {
                await this.stripe.products.update(product.id, { active: false })
                this.logger.info(`   ✅ Archived product: ${product.id} (${product.name})`)
                totalArchived++
              } else {
                this.logger.info(`   ⚠️  Product ${product.id} already archived`)
              }
            } catch (error: any) {
              this.logger.error(`   ❌ Failed to archive product ${product.id}: ${error.message}`)
            }
          }
        }

        hasMore = products.has_more
        if (hasMore && products.data.length > 0) {
          startingAfter = products.data[products.data.length - 1].id
        }
      }

      if (!this.dryRun) {
        this.logger.success(`   ✅ Archived ${totalArchived} products`)
      }
    } catch (error: any) {
      this.logger.error(`❌ Error cleaning products: ${error.message}`)
    }
  }
}
