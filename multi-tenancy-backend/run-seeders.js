#!/usr/bin/env node

/**
 * Simple script to run database seeders
 * This is a helper script for environments where ace commands might not be available
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 Running Database Seeders...');
console.log('================================');

try {
  // Change to the project directory
  process.chdir(__dirname);
  
  console.log('📍 Current directory:', process.cwd());
  
  // Run the seeder command
  console.log('\n🚀 Executing: node ace db:seed');
  
  const output = execSync('node ace db:seed', { 
    encoding: 'utf8',
    stdio: 'pipe'
  });
  
  console.log('\n✅ Seeders completed successfully!');
  console.log('\n📋 Output:');
  console.log(output);
  
  console.log('\n🎉 Database seeding completed!');
  console.log('\n👤 Default accounts created:');
  console.log('   🔑 Super Admin: <EMAIL> / SuperAdmin123!');
  console.log('   👑 Tenant Owner: <EMAIL> / DemoOwner123!');
  console.log('   👤 Tenant Admin: <EMAIL> / DemoAdmin123!');
  
} catch (error) {
  console.error('\n❌ Error running seeders:');
  console.error(error.message);
  
  if (error.stdout) {
    console.log('\n📤 stdout:', error.stdout.toString());
  }
  
  if (error.stderr) {
    console.error('\n📥 stderr:', error.stderr.toString());
  }
  
  console.log('\n💡 Make sure to:');
  console.log('   1. Install dependencies: npm install');
  console.log('   2. Set up your .env file with database configuration');
  console.log('   3. Run migrations first: node ace migration:run');
  
  process.exit(1);
}
