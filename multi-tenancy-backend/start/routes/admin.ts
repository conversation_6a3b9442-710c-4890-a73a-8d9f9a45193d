/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Routes for admin dashboard and management functionality
| Organized into: Public routes, Auth routes, Control routes, and Billing routes
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

// Admin API v1 routes
router
  .group(() => {
    // =================================================================
    // PUBLIC ROUTES (No authentication required)
    // =================================================================
    router
      .group(() => {
        // Authentication routes
        router.post('/register', '#controllers/auth_controller.register')
        router.post('/login', '#controllers/auth_controller.login')

        // Social authentication routes
        router.get('/google/redirect', '#controllers/auth_controller.googleRedirect')
        router.get('/google/callback', '#controllers/auth_controller.googleCallback')
      })
      .prefix('/auth')

    // Public pricing plans routes (no authentication required)
    router
      .group(() => {
        router.get('/', '#controllers/pricing_plans_controller.index')
        router.get('/popular', '#controllers/pricing_plans_controller.popular')
        router.get('/comparison', '#controllers/pricing_plans_controller.comparison')
      })
      .prefix('/pricing-plans')

    // =================================================================
    // AUTHENTICATED ROUTES (Require authentication)
    // =================================================================
    router
      .group(() => {
        // -------------------------
        // AUTH ROUTES (User management)
        // -------------------------
        router
          .group(() => {
            // User profile and session management
            router.get('/profile', '#controllers/auth_controller.profile')
            router.post('/update-profile', '#controllers/auth_controller.updateProfile')
            router.post('/change-password', '#controllers/auth_controller.changePassword')
            router.post('/logout', '#controllers/auth_controller.logout')
            router.get('/verify-token', '#controllers/auth_controller.verifyToken')
            router.get('/get-my-permissions', '#controllers/auth_controller.getMyPermissions')

            // Tenant context switching (for super admin)
            router.get('/tenants', '#controllers/auth_controller.getAvailableTenants')
          })
          .prefix('/auth')

        // -------------------------
        // ROLE & PERMISSION MANAGEMENT
        // -------------------------

        // Role management routes
        router.resource('roles', '#controllers/roles_controller')

        // Additional role routes (non-RESTful)
        router.group(() => {
          router.get('/permissions', '#controllers/roles_controller.permissions')
          router.post('/:id/permissions', '#controllers/roles_controller.assignPermissions')
        })

        // Available permissions endpoint
        router.get(
          '/permissions/available',
          '#controllers/user_permissions_controller.availablePermissions'
        )

        router.resource('users', '#controllers/users_controller')
        router.post('users/invite', '#controllers/users_controller.invite')

        // Additional user permission routes
        router.put('/users/:id/permissions', '#controllers/users_controller.updatePermissions')
        router.get('/users/:id/permissions', '#controllers/users_controller.getPermissions')

        router.resource('tenants', '#controllers/tenants_controller')

        // Additional tenant routes (non-RESTful)
        router.post('/tenants/create-simple', '#controllers/tenants_controller.createSimple')

        // -------------------------
        // BILLING MANAGEMENT
        // -------------------------

        // Tenant-scoped billing routes (current tenant billing info)
        router.get('/my-billing', '#controllers/billing_controller.current')

        // Stripe subscription routes
        router
          .group(() => {
            router.post(
              '/create-checkout-session',
              '#controllers/stripe_controller.createCheckoutSession'
            )
            router.post('/update-subscription', '#controllers/stripe_controller.updateSubscription')
            router.post(
              '/create-portal-session',
              '#controllers/stripe_controller.createPortalSession'
            )
          })
          .prefix('/stripe')
      })
      .use(middleware.auth())

    // Stripe webhook (no auth required)
    router.post('/stripe/webhook', '#controllers/stripe_controller.webhook')
  })
  .prefix('/api/v1/admin')

export default router
