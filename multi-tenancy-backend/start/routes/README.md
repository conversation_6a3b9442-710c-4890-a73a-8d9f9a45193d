# Routes Organization

This directory contains the organized route files for the Multi-Tenant SaaS API.

## Structure

### 1. `client.ts` - Client Routes (`/api/v1/client`)

Routes for customer-facing client site (e.g., product catalog, public pages).

**Purpose**: Used by end customers to view products, services, place orders, etc.

**Route Groups**:

- **Public Routes** (`/public`) - No authentication required

  - Product catalog (`/products`)
  - Categories (`/categories`)
  - Store information (`/store`)
  - Search functionality (`/search`)

- **Customer Auth** (`/auth`) - Customer authentication

  - Register, login, password reset

- **Protected Customer Routes** - Require customer authentication
  - Customer profile (`/customer`)
  - Orders (`/orders`)
  - Shopping cart (`/cart`)
  - Wishlist (`/wishlist`)

**Status**: 🚧 Not yet implemented (TODO comments in place)

### 2. `admin.ts` - Admin Routes (`/api/v1/admin`)

Routes for admin dashboard and management functionality.

**Purpose**: Used by tenant admins and staff to manage their tenant's operations.

**Route Groups**:

- **Public Routes** (`/auth`) - No authentication required

  - Login, register, password reset

- **Auth Routes** (`/auth`) - Require authentication

  - Profile management, logout, token verification
  - Tenant context switching (super admin)

- **Role & Permission Management**
  - Role CRUD operations (`/roles`)
  - Permission management
  - User role assignments (`/users/:userId/roles`)

**Status**: ✅ Implemented and active

## Route Prefixes

| Route File  | Prefix           | Purpose                       |
| ----------- | ---------------- | ----------------------------- |
| `client.ts` | `/api/v1/client` | Customer-facing functionality |
| `admin.ts`  | `/api/v1/admin`  | Tenant admin dashboard        |

## Authentication & Authorization

### Client Routes

- **Public routes**: No authentication
- **Customer routes**: Customer authentication (TODO: implement customer auth middleware)

### Admin Routes

- **Public routes**: No authentication (login, register)
- **Protected routes**: Admin authentication (`middleware.auth()`)
- **Permission-based**: Role-based permissions (`middleware.permission()`)

## Adding New Routes

### For Client Routes (customer-facing)

1. Add routes to `client.ts`
2. Create controllers in `app/controllers/client/`
3. Implement customer authentication middleware if needed

### For Admin Routes (tenant management)

1. Add routes to `admin.ts`
2. Use existing controllers or create new ones
3. Apply appropriate permission middleware

## Migration Notes

The routes have been reorganized from the original `routes.ts` file:

- **Moved**: All existing admin functionality to `admin.ts`
- **Added**: Structure for future client routes in `client.ts`
- **Maintained**: All existing functionality and middleware
- **Improved**: Better organization and separation of concerns

## Next Steps

1. **Implement Client Routes**: Add customer-facing functionality
2. **Add Customer Auth**: Create customer authentication system
3. **API Documentation**: Update API docs to reflect new structure
