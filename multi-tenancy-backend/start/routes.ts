/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The main routes file that imports organized route modules
|
*/

import router from '@adonisjs/core/services/router'

// Import organized route modules
import './routes/admin.js'

// Google OAuth callback route (matches ally config)
router.get('/google/callback', '#controllers/auth_controller.googleCallback')

// Health check endpoint
router.get('/', async () => {
  return {
    success: true,
    message: 'Multi-Tenant SaaS API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
  }
})
