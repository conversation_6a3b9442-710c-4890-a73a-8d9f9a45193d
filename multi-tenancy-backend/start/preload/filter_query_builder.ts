import { ModelQueryBuilder } from '@adonisjs/lucid/orm'
import type { LucidRow } from '@adonisjs/lucid/types/model'

declare module '@adonisjs/lucid/types/model' {
  interface ModelQueryBuilderContract<Model extends LucidModel, Result = InstanceType<Model>> {
    getCount(): Promise<BigInt>
  }
}

declare module '@adonisjs/lucid/orm' {
  interface ModelQueryBuilder {
    getCount(): Promise<BigInt>
  }
}

ModelQueryBuilder.macro('getCount', async function (this: ModelQueryBuilder) {
  return this.count('* as total')
    .first()
    .then((result: LucidRow) => {
      return BigInt(result.$extras.total)
    })
})
