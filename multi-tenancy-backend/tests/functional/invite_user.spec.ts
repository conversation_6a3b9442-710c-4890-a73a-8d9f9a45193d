import { test } from '@japa/runner'
import { ApiClient } from '@japa/api-client'

test.group('Invite User API', (group) => {
  let authToken: string
  let tenantId: number
  let roleId: number

  group.setup(async () => {
    // This would typically involve setting up test data
    // For now, we'll assume these values are available from test setup
    // authToken = 'test-auth-token'
    // tenantId = 1
    // roleId = 1
  })

  test('should successfully invite new user', async ({ client }) => {
    const response = await client
      .post('/api/admin/users/invite')
      .header('Authorization', `Bearer ${authToken}`)
      .header('X-Tenant-Id', tenantId.toString())
      .json({
        email: '<EMAIL>',
        roleId: roleId,
        permissions: []
      })

    response.assertStatus(200)
    response.assertBodyContains({
      success: true,
      message: 'User invited successfully'
    })
  })

  test('should return error when inviting duplicate user in same tenant', async ({ client }) => {
    const email = '<EMAIL>'

    // First invitation
    await client
      .post('/api/admin/users/invite')
      .header('Authorization', `Bearer ${authToken}`)
      .header('X-Tenant-Id', tenantId.toString())
      .json({
        email: email,
        roleId: roleId,
        permissions: []
      })

    // Second invitation should fail
    const response = await client
      .post('/api/admin/users/invite')
      .header('Authorization', `Bearer ${authToken}`)
      .header('X-Tenant-Id', tenantId.toString())
      .json({
        email: email,
        roleId: roleId,
        permissions: []
      })

    response.assertStatus(400)
    response.assertBodyContains({
      success: false,
      message: `User with email ${email} already exists in this tenant`
    })
  })

  test('should handle normalized email duplicates', async ({ client }) => {
    const email1 = '<EMAIL>'
    const email2 = '<EMAIL>' // Normalized version

    // First invitation
    await client
      .post('/api/admin/users/invite')
      .header('Authorization', `Bearer ${authToken}`)
      .header('X-Tenant-Id', tenantId.toString())
      .json({
        email: email1,
        roleId: roleId,
        permissions: []
      })

    // Second invitation with normalized email should fail
    const response = await client
      .post('/api/admin/users/invite')
      .header('Authorization', `Bearer ${authToken}`)
      .header('X-Tenant-Id', tenantId.toString())
      .json({
        email: email2,
        roleId: roleId,
        permissions: []
      })

    response.assertStatus(400)
    response.assertBodyContains({
      success: false
    })
  })
})
