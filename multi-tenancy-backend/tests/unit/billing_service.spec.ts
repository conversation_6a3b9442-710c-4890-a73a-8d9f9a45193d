import { test } from '@japa/runner'
import BillingService from '#services/billing_service'
import Billing, { BillingStatus, BillingPlan, BillingCycle } from '#models/billing'
import Tenant from '#models/tenant'
import { DateTime } from 'luxon'
import NotFoundException from '#exceptions/not_found_exception'

test.group('BillingService', () => {
  test('findOrFail should return billing record when it exists', async ({ assert }) => {
    // Create a test tenant first
    const tenant = await Tenant.create({
      name: 'Test Tenant',
      slug: 'test-tenant',
      status: 'active',
    })

    // Create a test billing record
    const billing = await Billing.create({
      tenantId: tenant.id,
      plan: BillingPlan.BASIC,
      cycle: BillingCycle.MONTHLY,
      status: BillingStatus.ACTIVE,
      amount: 2900,
      currency: 'usd',
      maxUsers: 10,
      maxStorage: 10,
      features: JSON.stringify(['basic_support', 'email_support']),
      currentPeriodStart: DateTime.now(),
      currentPeriodEnd: DateTime.now().plus({ months: 1 }),
      cancelAtPeriodEnd: false,
    })

    const billingService = new BillingService()

    // Test findOrFail with existing record
    const foundBilling = await billingService.findOrFail(billing.id)

    assert.equal(foundBilling.id, billing.id)
    assert.equal(foundBilling.plan, BillingPlan.BASIC)
    assert.equal(foundBilling.tenantId, tenant.id)
  })

  test('findOrFail should throw NotFoundException when billing record does not exist', async ({
    assert,
  }) => {
    const billingService = new BillingService()

    // Test findOrFail with non-existent record
    await assert.rejects(
      () => billingService.findOrFail(99999),
      NotFoundException,
      'Billing not found'
    )
  })

  test('getPaginated should return billing records with pagination', async ({ assert }) => {
    const billingService = new BillingService()

    // Test getPaginated method
    const result = await billingService.getPaginated({ page: 1, limit: 10 })

    assert.isArray(result.data)
    assert.isObject(result.pagination)
    assert.property(result.pagination, 'currentPage')
    assert.property(result.pagination, 'perPage')
    assert.property(result.pagination, 'total')
  })
})
