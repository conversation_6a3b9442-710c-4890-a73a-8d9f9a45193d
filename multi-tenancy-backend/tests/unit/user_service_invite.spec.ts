import { test } from '@japa/runner'
import User from '#models/user'
import Tenant from '#models/tenant'
import Role from '#models/role'
import UserService from '#services/user_service'
import UserRoleService from '#services/user_role_service'
import UserPermissionService from '#services/user_permission_service'
import BadRequestException from '#exceptions/bad_request_exception'

test.group('UserService - Invite User', (group) => {
  let userService: UserService
  let userRoleService: UserRoleService
  let userPermissionService: UserPermissionService
  let tenant1: Tenant
  let tenant2: Tenant
  let role: Role

  group.setup(async () => {
    userRoleService = new UserRoleService()
    userPermissionService = new UserPermissionService()
    userService = new UserService(userRoleService, userPermissionService)

    // Create test tenants
    tenant1 = await Tenant.create({
      name: 'Test Tenant 1',
      slug: 'test-tenant-1',
    })

    tenant2 = await Tenant.create({
      name: 'Test Tenant 2',
      slug: 'test-tenant-2',
    })

    // Create test role
    role = await Role.create({
      name: 'test-role',
      displayName: 'Test Role',
    })
  })

  group.teardown(async () => {
    // Clean up test data
    await User.query().delete()
    await Tenant.query().delete()
    await Role.query().delete()
  })

  test('should successfully invite new user to tenant', async ({ assert }) => {
    const email = '<EMAIL>'

    const user = await userService.inviteUser(email, role.id, tenant1.id)

    assert.exists(user)
    assert.equal(user.email, email)

    // Verify user is associated with the tenant
    await user.load('tenants')
    assert.equal(user.tenants.length, 1)
    assert.equal(user.tenants[0].id, tenant1.id)
  })

  test('should successfully invite existing user from another tenant', async ({ assert }) => {
    const email = '<EMAIL>'

    // First, create user in tenant1
    const user1 = await userService.inviteUser(email, role.id, tenant1.id)

    // Then, invite same user to tenant2 (should succeed)
    const user2 = await userService.inviteUser(email, role.id, tenant2.id)

    assert.equal(user1.id, user2.id) // Same user

    // Verify user is now associated with both tenants
    await user2.load('tenants')
    assert.equal(user2.tenants.length, 2)

    const tenantIds = user2.tenants.map((t) => t.id).sort()
    assert.deepEqual(tenantIds, [tenant1.id, tenant2.id].sort())
  })

  test('should throw exception when inviting user that already exists in same tenant', async ({
    assert,
  }) => {
    const email = '<EMAIL>'

    // First invitation should succeed
    await userService.inviteUser(email, role.id, tenant1.id)

    // Second invitation to same tenant should fail
    await assert.rejects(
      () => userService.inviteUser(email, role.id, tenant1.id),
      BadRequestException,
      'User <NAME_EMAIL> already exists in this tenant'
    )
  })

  test('should handle email normalization when checking duplicates', async ({ assert }) => {
    const email1 = '<EMAIL>'
    const email2 = '<EMAIL>' // Normalized version of email1

    // First invitation should succeed
    await userService.inviteUser(email1, role.id, tenant1.id)

    // Second invitation with normalized email should fail
    await assert.rejects(
      () => userService.inviteUser(email2, role.id, tenant1.id),
      BadRequestException
    )
  })

  test('should successfully invite user without permissions', async ({ assert }) => {
    const email = '<EMAIL>'

    // Invite user without permissions
    const user = await userService.inviteUser(email, role.id, tenant1.id)

    assert.exists(user)
    assert.equal(user.email, email)

    // Verify user is associated with the tenant
    await user.load('tenants')
    assert.equal(user.tenants.length, 1)
    assert.equal(user.tenants[0].id, tenant1.id)
  })

  test('should successfully invite user with empty permissions array', async ({ assert }) => {
    const email = '<EMAIL>'

    // Invite user with empty permissions array
    const user = await userService.inviteUser(email, role.id, tenant1.id, [])

    assert.exists(user)
    assert.equal(user.email, email)

    // Verify user is associated with the tenant
    await user.load('tenants')
    assert.equal(user.tenants.length, 1)
    assert.equal(user.tenants[0].id, tenant1.id)
  })
})
