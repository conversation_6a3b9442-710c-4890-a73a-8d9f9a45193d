import { test } from '@japa/runner'
import User, { UserStatus } from '#models/user'
import Tenant from '#models/tenant'
import Role from '#models/role'
import UserRole from '#models/user_role'
import UserService from '#services/user_service'
import UserRoleService from '#services/user_role_service'
import UserPermissionService from '#services/user_permission_service'

test.group('UserService - Delete User', (group) => {
  let userService: UserService
  let userRoleService: UserRoleService
  let userPermissionService: UserPermissionService
  let tenant1: Tenant
  let tenant2: Tenant
  let role: Role
  let user: User

  group.setup(async () => {
    userRoleService = new UserRoleService()
    userPermissionService = new UserPermissionService()
    userService = new UserService(userRoleService, userPermissionService)

    // Create test tenants
    tenant1 = await Tenant.create({
      name: 'Test Tenant 1',
      slug: 'test-tenant-1',
    })

    tenant2 = await Tenant.create({
      name: 'Test Tenant 2',
      slug: 'test-tenant-2',
    })

    // Create test role
    role = await Role.create({
      name: 'test-role',
      displayName: 'Test Role',
    })

    // Create test user
    user = await User.create({
      firstName: 'Test',
      lastName: 'User',
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      status: UserStatus.ACTIVE,
    })
  })

  group.teardown(async () => {
    // Clean up test data
    await UserRole.query().delete()
    await User.query().delete()
    await Tenant.query().delete()
    await Role.query().delete()
  })

  test('should remove user from tenant without affecting user record', async ({ assert }) => {
    // Assign user to both tenants
    await userRoleService.assignRole(user, role.id, tenant1.id)
    await userRoleService.assignRole(user, role.id, tenant2.id)

    // Verify user is in both tenants
    const userRolesBefore = await UserRole.query().where('userId', user.id)
    assert.equal(userRolesBefore.length, 2)

    // Set tenant context and delete user from tenant1
    userService.filterByTenant(tenant1.id)
    await userService.deleteUser(user.id)

    // Verify user record still exists
    const userAfterDelete = await User.find(user.id)
    assert.exists(userAfterDelete)
    assert.equal(userAfterDelete!.status, UserStatus.ACTIVE) // Status should not change

    // Verify user is removed from tenant1 but still in tenant2
    const userRolesAfter = await UserRole.query().where('userId', user.id)
    assert.equal(userRolesAfter.length, 1)
    assert.equal(userRolesAfter[0].tenantId, tenant2.id)
  })

  test('should remove all roles for user in specific tenant', async ({ assert }) => {
    // Create another role
    const role2 = await Role.create({
      name: 'test-role-2',
      displayName: 'Test Role 2',
    })

    // Assign user multiple roles in tenant1
    await userRoleService.assignRole(user, role.id, tenant1.id)
    await userRoleService.assignRole(user, role2.id, tenant1.id)

    // Verify user has 2 roles in tenant1
    const userRolesBefore = await UserRole.query()
      .where('userId', user.id)
      .where('tenantId', tenant1.id)
    assert.equal(userRolesBefore.length, 2)

    // Delete user from tenant1
    userService.filterByTenant(tenant1.id)
    await userService.deleteUser(user.id)

    // Verify all roles are removed from tenant1
    const userRolesAfter = await UserRole.query()
      .where('userId', user.id)
      .where('tenantId', tenant1.id)
    assert.equal(userRolesAfter.length, 0)

    // Clean up
    await role2.delete()
  })

  test('should throw error when user not found', async ({ assert }) => {
    userService.filterByTenant(tenant1.id)
    
    await assert.rejects(
      () => userService.deleteUser(99999),
      Error,
      'User not found'
    )
  })
})
