import { test } from '@japa/runner'
import inviteUserValidator from '#validators/invite_user'

test.group('Invite User Validator', () => {
  test('should validate payload with permissions', async ({ assert }) => {
    const payload = {
      email: '<EMAIL>',
      roleId: 1,
      permissions: ['users.read', 'users.create'],
    }

    const result = await inviteUserValidator.validate(payload)

    assert.equal(result.email, payload.email)
    assert.equal(result.roleId, payload.roleId)
    assert.deepEqual(result.permissions, payload.permissions)
  })

  test('should validate payload without permissions', async ({ assert }) => {
    const payload = {
      email: '<EMAIL>',
      roleId: 5,
    }

    const result = await inviteUserValidator.validate(payload)

    assert.equal(result.email, payload.email)
    assert.equal(result.roleId, payload.roleId)
    assert.isUndefined(result.permissions)
  })

  test('should validate payload with empty permissions array', async ({ assert }) => {
    const payload = {
      email: '<EMAIL>',
      roleId: 1,
      permissions: [],
    }

    const result = await inviteUserValidator.validate(payload)

    assert.equal(result.email, payload.email)
    assert.equal(result.roleId, payload.roleId)
    assert.deepEqual(result.permissions, [])
  })

  test('should reject invalid email', async ({ assert }) => {
    const payload = {
      email: 'invalid-email',
      roleId: 1,
      permissions: [],
    }

    await assert.rejects(() => inviteUserValidator.validate(payload))
  })

  test('should reject missing roleId', async ({ assert }) => {
    const payload = {
      email: '<EMAIL>',
      permissions: [],
    }

    await assert.rejects(() => inviteUserValidator.validate(payload))
  })
})
