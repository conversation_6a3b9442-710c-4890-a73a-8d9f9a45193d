import { test } from '@japa/runner'
import { normalizeEmail, emailsMatch, emailExistsInList } from '#utils/email_helper'

test.group('Email Helper', () => {
  test('normalizeEmail should convert email to lowercase', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('normalizeEmail should remove dots from Gmail addresses', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('normalizeEmail should remove plus addressing from Gmail', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('normalizeEmail should handle both dots and plus in Gmail', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('normalizeEmail should treat googlemail.com same as gmail.com', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('normalizeEmail should not modify non-Gmail addresses', ({ assert }) => {
    const result = normalizeEmail('<EMAIL>')
    assert.equal(result, '<EMAIL>')
  })

  test('emailsMatch should return true for equivalent emails', ({ assert }) => {
    assert.isTrue(emailsMatch('<EMAIL>', '<EMAIL>'))
    assert.isTrue(emailsMatch('<EMAIL>', '<EMAIL>'))
    assert.isTrue(emailsMatch('<EMAIL>', '<EMAIL>'))
  })

  test('emailsMatch should return false for different emails', ({ assert }) => {
    assert.isFalse(emailsMatch('<EMAIL>', '<EMAIL>'))
    assert.isFalse(emailsMatch('<EMAIL>', '<EMAIL>'))
  })

  test('emailExistsInList should find equivalent emails', ({ assert }) => {
    const emailList = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

    // These <NAME_EMAIL> (which <NAME_EMAIL>)
    assert.isTrue(emailExistsInList('<EMAIL>', emailList))
    assert.isTrue(emailExistsInList('<EMAIL>', emailList)) // This will <NAME_EMAIL>
    assert.isTrue(emailExistsInList('<EMAIL>', emailList))
    assert.isFalse(emailExistsInList('<EMAIL>', emailList))
  })

  test('should handle edge cases gracefully', ({ assert }) => {
    assert.equal(normalizeEmail(''), '')
    assert.equal(normalizeEmail('invalid-email'), 'invalid-email')
    assert.isFalse(emailsMatch('', '<EMAIL>'))
    assert.isFalse(emailsMatch('<EMAIL>', ''))
    assert.isFalse(emailExistsInList('<EMAIL>', []))
  })
})
