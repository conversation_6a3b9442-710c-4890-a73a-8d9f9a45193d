# Multi-Tenant SaaS Backend

A comprehensive multi-tenant SaaS backend built with AdonisJS, featuring authentication and merchant management with role-based access control.

## 🚀 Features

### Authentication System

- ✅ User registration and login
- ✅ JWT token-based authentication
- ✅ Role-based access control (Super Admin, Tenant Owner, Tenant Admin, Manager, Staff, Viewer)
- ✅ Profile management
- ✅ Password change functionality
- ✅ Token verification

### Merchant Management

- ✅ Full CRUD operations (Create, Read, Update, Delete)
- ✅ Status management (Active, Inactive, Suspended, Pending)
- ✅ Business information management
- ✅ Contact information and address
- ✅ Business hours and payment settings
- ✅ Branding and tags
- ✅ Analytics dashboard (foundation)
- ✅ **Super Admin only access** for merchant operations

### Multi-Tenancy

- ✅ Tenant isolation
- ✅ Domain-based tenant resolution
- ✅ Tenant-specific user management

## 🏗️ Architecture

### Database Schema

- **Users**: Enhanced with roles, tenant relationships, and profile information
- **Tenants**: Multi-tenant isolation with settings and status management
- **Merchants**: Complete business profile management
- **Access Tokens**: JWT token management for authentication

### Role Hierarchy

1. **Super Admin**: Full system access, can manage all merchants across all tenants
2. **Tenant Owner**: Tenant-level management (future implementation)
3. **Tenant Admin**: Tenant administration (future implementation)
4. **Manager**: Limited management access (future implementation)
5. **Staff**: Basic operational access (future implementation)
6. **Viewer**: Read-only access (future implementation)

## 📋 API Endpoints

### Base URL

- **Local Development**: `http://localhost:3333/api/v1`
- **With Subdomain**: `http://tenant-name.localhost:3333/api/v1`

### Authentication Endpoints

| Method | Endpoint                | Description       | Auth Required |
| ------ | ----------------------- | ----------------- | ------------- |
| POST   | `/auth/register`        | Register new user | No            |
| POST   | `/auth/login`           | User login        | No            |
| GET    | `/auth/profile`         | Get user profile  | Yes           |
| POST   | `/auth/update-profile`  | Update profile    | Yes           |
| POST   | `/auth/change-password` | Change password   | Yes           |
| POST   | `/auth/logout`          | User logout       | Yes           |
| GET    | `/auth/verify-token`    | Verify JWT token  | Yes           |

### Merchant Endpoints (Super Admin Only)

| Method | Endpoint                   | Description          | Auth Required     |
| ------ | -------------------------- | -------------------- | ----------------- |
| POST   | `/merchants`               | Create merchant      | Yes (Super Admin) |
| GET    | `/merchants`               | List merchants       | Yes (Super Admin) |
| GET    | `/merchants/:id`           | Get merchant details | Yes (Super Admin) |
| PATCH  | `/merchants/:id`           | Update merchant      | Yes (Super Admin) |
| DELETE | `/merchants/:id`           | Delete merchant      | Yes (Super Admin) |
| PATCH  | `/merchants/:id/status`    | Update status        | Yes (Super Admin) |
| GET    | `/merchants/:id/analytics` | Get analytics        | Yes (Super Admin) |

## 🛠️ Setup Instructions

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- MySQL/PostgreSQL database

### Installation

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Environment Configuration**
   Create a `.env` file:

   ```env
   NODE_ENV=development
   PORT=3333
   HOST=0.0.0.0
   LOG_LEVEL=info
   APP_KEY=your-secret-app-key

   # Database
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_USER=your-db-user
   DB_PASSWORD=your-db-password
   DB_DATABASE=multitenant_saas
   ```

3. **Database Setup**

   ```bash
   # Quick setup (recommended)
   node setup-database.js

   # Or run manually:
   node ace migration:run
   node ace db:seed
   ```

4. **Start the server**

   ```bash
   # Development
   npm run dev

   # Production
   npm run build
   npm start
   ```

## 👤 Default Accounts

After running the seeder, these accounts will be available:

### Super Admin

- **Email**: `<EMAIL>`
- **Password**: `SuperAdmin123!`
- **Role**: Super Admin
- **Access**: Full system access, can manage all merchants

### Demo Tenant Owner

- **Email**: `<EMAIL>`
- **Password**: `DemoOwner123!`
- **Role**: Tenant Owner
- **Tenant**: demo-tenant

### Demo Tenant Admin

- **Email**: `<EMAIL>`
- **Password**: `DemoAdmin123!`
- **Role**: Tenant Admin
- **Tenant**: demo-tenant

## 🌱 Database Seeders

The application includes database seeders to populate initial data:

### Available Seeders

- **SuperAdminSeeder**: Creates super admin user and demo tenant
- **MainSeeder**: Orchestrates all seeders in proper order

### Running Seeders

```bash
# Run all seeders
node ace db:seed

# Run specific seeder
node ace db:seed --files "./database/seeders/super_admin_seeder.ts"

# Interactive mode
node ace db:seed -i

# Using helper script
node run-seeders.js
```

### Seeder Features

- **Idempotent**: Safe to run multiple times without creating duplicates
- **Environment-specific**: Only runs in development and testing environments
- **Ordered execution**: Proper dependency management between seeders

## 🧪 Testing

### Automated Testing

Run the comprehensive API test suite:

```bash
node test-auth-api.js
```

### Manual Testing with cURL

#### 1. Super Admin Login

```bash
curl -X POST http://localhost:3333/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin123!"
  }'
```

#### 2. Create Merchant (Super Admin only)

```bash
curl -X POST http://localhost:3333/api/v1/merchants \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "businessName": "Test Coffee Shop",
    "businessDescription": "A test coffee shop",
    "businessType": "restaurant",
    "contactInfo": {
      "email": "<EMAIL>",
      "phone": "+1234567890"
    },
    "address": {
      "street": "123 Main St",
      "city": "Test City",
      "state": "TS",
      "country": "USA",
      "postalCode": "12345"
    }
  }'
```

#### 3. Get All Merchants

```bash
curl -X GET http://localhost:3333/api/v1/merchants \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔐 Security Features

- **JWT Authentication**: Stateless token-based authentication
- **Role-based Access Control**: Granular permission system
- **Password Hashing**: Secure password storage with scrypt
- **Input Validation**: Comprehensive request validation
- **Tenant Isolation**: Complete data separation between tenants
- **CORS Configuration**: Secure cross-origin resource sharing

## 📊 Response Format

### Success Response

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    /* response data */
  },
  "pagination": {
    /* pagination info for lists */
  }
}
```

### Error Response

```json
{
  "success": false,
  "message": "User-friendly error message",
  "error": "Detailed error information"
}
```

## 🚀 Next Steps

1. **Run the setup commands** to initialize the database
2. **Test the API** using the provided test script
3. **Integrate with your frontend** application
4. **Extend functionality** as needed for your specific use case

## 📝 Notes

- Only **Super Admin** users can perform merchant CRUD operations
- All API responses follow a consistent format
- The system is designed for easy extension with additional features
- Multi-tenant isolation ensures data security between tenants
