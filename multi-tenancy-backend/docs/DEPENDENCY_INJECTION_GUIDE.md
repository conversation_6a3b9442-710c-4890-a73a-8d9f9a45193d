# Dependency Injection Guide

This guide explains how to use dependency injection in the multi-tenancy backend application, specifically focusing on the RBAC (Role-Based Access Control) system.

## Overview

We've refactored the User model to follow the Single Responsibility Principle by moving all RBAC helper methods to a dedicated `UserRoleService`. This service is injected into controllers and other services using AdonisJS's IoC container.

## Key Benefits

1. **Separation of Concerns**: User model focuses on user data, service handles RBAC logic
2. **Testability**: Easy to mock services in tests
3. **Reusability**: Services can be used across multiple controllers
4. **Maintainability**: Centralized business logic

## Services

### UserRoleService

Located at `app/services/user_role_service.ts`

**Key Methods:**
- `getUserRoles(user, tenantId?)` - Get user roles
- `getUserPermissions(user, tenantId?)` - Get user permissions
- `hasPermission(user, permission, tenantId?)` - Check single permission
- `hasAnyPermission(user, permissions, tenantId?)` - Check any of multiple permissions
- `hasAllPermissions(user, permissions, tenantId?)` - Check all permissions
- `hasRole(user, roleName, tenantId?)` - Check if user has role
- `assignRole(user, roleId, tenantId?)` - Assign role to user
- `removeRole(user, roleId, tenantId?)` - Remove role from user

### MerchantService

Located at `app/services/merchant_service.ts`

**Key Methods:**
- `canCreateMerchant(user, tenantId?)` - Check creation permissions
- `canViewMerchant(user, merchant)` - Check view permissions
- `canEditMerchant(user, merchant)` - Check edit permissions
- `canDeleteMerchant(user, merchant)` - Check delete permissions
- `getAccessibleMerchants(user, tenantId?)` - Get merchants user can access

## Usage Examples

### 1. Controller with Dependency Injection

```typescript
import { inject } from '@adonisjs/core'
import UserRoleService from '#services/user_role_service'

@inject()
export default class MyController {
  constructor(protected userRoleService: UserRoleService) {}

  async someMethod({ auth, response }: HttpContext) {
    const user = auth.user!
    
    // Check if user has permission
    const canManage = await this.userRoleService.hasPermission(
      user, 
      'merchants.manage', 
      user.tenantId
    )
    
    if (!canManage) {
      return response.forbidden({ message: 'Access denied' })
    }
    
    // Continue with business logic...
  }
}
```

### 2. Service with Dependency Injection

```typescript
import { inject } from '@adonisjs/core'
import UserRoleService from '#services/user_role_service'

@inject()
export default class MyService {
  constructor(protected userRoleService: UserRoleService) {}

  async businessLogic(user: User) {
    const permissions = await this.userRoleService.getUserPermissions(user)
    // Use permissions for business logic
  }
}
```

### 3. Middleware with Dependency Injection

```typescript
import { inject } from '@adonisjs/core'
import UserRoleService from '#services/user_role_service'

@inject()
export default class RbacMiddleware {
  constructor(protected userRoleService: UserRoleService) {}

  async handle(ctx: HttpContext, next: NextFn, options: { permission: string }) {
    const user = ctx.auth.user!
    const hasPermission = await this.userRoleService.hasPermission(
      user, 
      options.permission
    )
    
    if (!hasPermission) {
      return ctx.response.forbidden({ message: 'Access denied' })
    }
    
    await next()
  }
}
```

## Migration from Old Pattern

### Before (Direct Model Methods)
```typescript
// ❌ Old way - calling methods directly on User model
const roles = await user.getRoles(tenantId)
const permissions = await user.getPermissions(tenantId)
const hasPermission = await user.hasPermission('merchants.create')
await user.assignRole(roleId, tenantId)
```

### After (Service Injection)
```typescript
// ✅ New way - using injected service
@inject()
export default class MyController {
  constructor(protected userRoleService: UserRoleService) {}

  async method({ auth }: HttpContext) {
    const user = auth.user!
    
    const roles = await this.userRoleService.getUserRoles(user, tenantId)
    const permissions = await this.userRoleService.getUserPermissions(user, tenantId)
    const hasPermission = await this.userRoleService.hasPermission(user, 'merchants.create')
    await this.userRoleService.assignRole(user, roleId, tenantId)
  }
}
```

## Testing with Dependency Injection

### Mocking Services in Tests

```typescript
import { test } from '@japa/runner'
import UserRoleService from '#services/user_role_service'

test('should check permissions', async ({ client }) => {
  // Create a mock service
  class MockUserRoleService extends UserRoleService {
    async hasPermission() {
      return true // Always return true for test
    }
  }

  // Swap the service in container
  app.container.swap(UserRoleService, () => new MockUserRoleService())

  const response = await client.get('/protected-route')
  
  // Restore original service
  app.container.restore(UserRoleService)
})
```

## Best Practices

1. **Always use @inject() decorator** on classes that need dependency injection
2. **Inject services in constructor** for better testability
3. **Use protected/private access modifiers** for injected dependencies
4. **Create specific services** for different business domains (UserRoleService, MerchantService, etc.)
5. **Keep services focused** on single responsibilities
6. **Use interfaces** for better abstraction when needed

## Available Services

- `UserRoleService` - RBAC operations
- `MerchantService` - Merchant-specific business logic
- `RbacMiddleware` - Permission checking middleware

## Next Steps

1. Create more domain-specific services as needed
2. Add interfaces for better abstraction
3. Implement caching in services for better performance
4. Add comprehensive tests for all services
