# SaaS RBAC System Documentation

## Overview

This document describes the Role-Based Access Control (RBAC) system implemented for the multi-tenant SaaS application.

## Role Hierarchy

### 1. Super Admin (`super_admin`)

- **Global Role**: Not tied to any specific tenant (`tenantId = null`)
- **Permissions**: Can control everything in the system
- **Capabilities**:
  - Create, read, update, delete tenants
  - Manage all users across all tenants
  - Access any tenant's dashboard by switching context
  - Manage system-wide settings and permissions
  - View analytics across all tenants

### 2. Tenant Owner (`tenant_owner`)

- **Tenant-Specific Role**: Tied to a specific tenant (`tenantId = specific_tenant_id`)
- **Permissions**: Can manage their own tenant and business logic within it
- **Capabilities**:
  - Update their own tenant profile (name, settings, etc.)
  - Cannot create, delete, or manage other tenants
  - Manage users within their tenant
  - Access business logic modules (outlets, products, orders, etc.)
  - View analytics for their tenant only

### 3. Tenant User (`tenant_user`) - Future Implementation

- **Tenant-Specific Role**: Tied to a specific tenant
- **Permissions**: Limited access to business logic within their tenant
- **Capabilities**:
  - Access specific business modules based on assigned permissions
  - Cannot manage tenant settings or other users

### 4. Staff (`staff`) - Future Implementation

- **Tenant-Specific Role**: Tied to a specific tenant
- **Permissions**: Very limited access for operational tasks
- **Capabilities**:
  - Access only assigned operational modules
  - Cannot manage users or tenant settings

## Permission Structure

### System Permissions (Super Admin Only)

- `tenants.create` - Create new tenants
- `tenants.delete` - Delete tenants
- `tenants.manage` - Manage tenant status and system-level settings
- `system.admin` - Full system administration access

### Tenant-Level Permissions

- `tenants.read` - View tenant information
- `tenants.update` - Update tenant profile (own tenant only for tenant_owner)
- `analytics.read` - View analytics data
- `users.read` - View users within tenant
- `users.create` - Create users within tenant
- `users.update` - Update users within tenant
- `users.delete` - Delete users within tenant
- `users.manage` - Full user management within tenant

### Business Logic Permissions (Future)

- `outlets.read` - View outlets
- `outlets.create` - Create outlets
- `outlets.update` - Update outlets
- `outlets.delete` - Delete outlets
- `products.read` - View products
- `products.create` - Create products
- `products.update` - Update products
- `products.delete` - Delete products

## Middleware Configuration

### Permission Middleware Options

```typescript
{
  permissions: string[]           // Required permissions
  requireAll?: boolean           // Require ALL permissions (default: false - ANY)
  requireTenantOwnership?: boolean // Validate tenant ownership
}
```

### Usage Examples

#### Super Admin Access (Automatic)

```typescript
middleware.permission({
  permissions: ['tenants.create'],
})
// Super admin automatically bypasses permission checks
```

#### Tenant Owner Access (Own Tenant Only)

```typescript
middleware.permission({
  permissions: ['tenants.update'],
  requireTenantOwnership: true,
})
// Validates user owns the tenant specified in X-Tenant-Id header
```

#### Business Logic Access

```typescript
middleware.permission({
  permissions: ['outlets.read'],
  requireTenantOwnership: true,
})
// Requires tenant context and validates ownership
```

## Tenant Context Extraction

The middleware extracts tenant context only from:

1. **Headers**: `X-Tenant-Id: 123` (for tenant context switching)

This approach ensures clean separation between tenant context and resource identification, making the system more secure and predictable.

## Admin Dashboard Considerations

### Navigation Structure

#### Super Admin View

- **System Management**
  - Tenant Management (CRUD)
  - User Management (Global)
  - System Settings
  - Analytics Dashboard
- **Tenant Context Switcher**
  - Dropdown to select which tenant to manage
  - Access to tenant-specific business modules

#### Tenant Owner View

- **Tenant Profile** (Own tenant only)
- **Business Modules**
  - Outlets Management (Future)
  - Products Management (Future)
  - Orders Management (Future)
  - Staff Management (Future)
- **Analytics** (Own tenant only)

### Implementation Notes

1. **Remove Tenant CRUD from Side Navigation** for non-super-admin users
2. **Show Business Logic Modules** based on user permissions
3. **Tenant Context Switching** only available for super admins
4. **Profile Management** limited to own tenant for tenant owners

## Database Schema

### User Roles Table (`user_roles`)

```sql
CREATE TABLE user_roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  role_id INT NOT NULL,
  tenant_id INT NULL,  -- NULL for global roles (super_admin)
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (role_id) REFERENCES roles(id),
  FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
```

### Example Data

#### Super Admin User

```sql
INSERT INTO user_roles (user_id, role_id, tenant_id)
VALUES (1, 1, NULL);  -- super_admin role, no tenant
```

#### Tenant Owner User

```sql
INSERT INTO user_roles (user_id, role_id, tenant_id)
VALUES (2, 2, 1);  -- tenant_owner role, tenant_id = 1
```

## Security Considerations

1. **Tenant Isolation**: Users can only access their own tenant's data
2. **Super Admin Bypass**: Can be disabled for sensitive operations
3. **Ownership Validation**: Ensures users can only modify their own tenant
4. **Permission Inheritance**: Roles inherit permissions through role_permissions table
5. **Context Validation**: Tenant context is validated on every request

## Future Enhancements

1. **Dynamic Permissions**: Allow tenant owners to create custom roles
2. **Permission Inheritance**: Hierarchical permission structure
3. **Audit Logging**: Track all permission-based actions
4. **Session Management**: Tenant context persistence across requests
5. **API Rate Limiting**: Per-tenant rate limiting
