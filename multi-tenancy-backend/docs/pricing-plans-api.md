# Pricing Plans API

This document describes the Pricing Plans API endpoints that provide read-only access to pricing plan information.

## Base URL

All endpoints are prefixed with `/api/v1/pricing-plans`

## Authentication

These endpoints are **public** and do not require authentication.

## Endpoints

### 1. Get All Pricing Plans

**GET** `/api/v1/pricing-plans`

Returns all active pricing plans ordered by sort order.

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Free",
      "slug": "free",
      "description": "Perfect for getting started with basic features",
      "monthlyPrice": 0,
      "yearlyPrice": 0,
      "currency": "usd",
      "isPopular": false,
      "features": [...],
      "limits": {
        "maxUsers": 3,
        "maxStorage": 1,
        "maxApiCalls": null,
        "maxProjects": 1
      },
      "trialDays": 14,
      "yearlySavings": 0,
      "createdAt": "2025-07-12T13:06:15.000+00:00",
      "updatedAt": "2025-07-12T13:06:15.000+00:00"
    }
  ]
}
```

### 2. Get Popular Pricing Plans

**GET** `/api/v1/pricing-plans/popular`

Returns only the plans marked as popular.

### 3. Get Plans Comparison

**GET** `/api/v1/pricing-plans/comparison`

Returns all plans with comparison-friendly data structure.

### 4. Get Specific Plan

**GET** `/api/v1/pricing-plans/{slug}`

Returns details for a specific plan by slug.

**Parameters:**

- `slug` (string): Plan slug (free, basic, pro, enterprise)

### 5. Get Plan Price

**GET** `/api/v1/pricing-plans/{slug}/price?cycle={cycle}`

Returns the price for a specific plan and billing cycle.

**Parameters:**

- `slug` (string): Plan slug
- `cycle` (query): Billing cycle (monthly, yearly) - defaults to monthly

**Response:**

```json
{
  "success": true,
  "data": {
    "slug": "pro",
    "cycle": "monthly",
    "price": 9900,
    "currency": "usd"
  }
}
```

### 6. Get Plan Features

**GET** `/api/v1/pricing-plans/{slug}/features`

Returns the features list for a specific plan.

**Response:**

```json
{
  "success": true,
  "data": {
    "slug": "pro",
    "features": [
      {
        "name": "API Access",
        "description": "RESTful API access for integrations",
        "included": true,
        "limit": 10000
      }
    ]
  }
}
```

### 7. Get Upgrade Options

**GET** `/api/v1/pricing-plans/{slug}/upgrade-options`

Returns available upgrade options for the current plan.

**Response:**

```json
{
  "success": true,
  "data": {
    "currentPlan": "basic",
    "canUpgrade": true,
    "nextUpgrade": {
      "id": 3,
      "name": "Pro",
      "slug": "pro",
      "monthlyPrice": 9900,
      "yearlyPrice": 99000
    },
    "upgradePlans": [...]
  }
}
```

## Data Structures

### Plan Feature

```json
{
  "name": "Feature Name",
  "description": "Feature description",
  "included": true,
  "limit": 1000 // Optional: numeric limit for the feature
}
```

### Plan Limits

```json
{
  "maxCustomers": 1000, // null means unlimited
  "maxLocations": 3, // Number of store locations, null means unlimited
  "maxCampaigns": 5, // Marketing campaigns per month, null means unlimited
  "maxRewards": 15 // Number of reward types, null means unlimited
}
```

## Available Plans

1. **Free** (`free`)

   - $0/month, $0/year
   - 100 customers, 1 location, 1 campaign, 5 rewards
   - Basic loyalty program with customer management
   - 14-day trial

2. **Basic** (`basic`)

   - $29/month, $290/year (17% savings)
   - 1,000 customers, 3 locations, 5 campaigns, 15 rewards
   - Advanced rewards and basic marketing campaigns
   - 14-day trial

3. **Pro** (`pro`) - **Popular**

   - $99/month, $990/year (17% savings)
   - 10,000 customers, unlimited locations, 25 campaigns, 50 rewards
   - Advanced analytics, API access, custom branding
   - 14-day trial

4. **Enterprise** (`enterprise`)
   - $299/month, $2990/year (17% savings)
   - Unlimited everything
   - 30-day trial

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

Common HTTP status codes:

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Not Found (plan not found)
- `500` - Internal Server Error
