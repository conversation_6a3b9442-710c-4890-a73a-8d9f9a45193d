# Permission Decorators for SaaS RBAC System

This directory contains custom decorators that integrate with the existing permission middleware and bouncer policies to provide method-level authorization for your SaaS multi-tenant application.

## Available Decorators

### 1. `@hasPermission(permission, options)`

Checks if the authenticated user has a specific permission.

**Parameters:**

- `permission` (string): Permission in format 'resource.action' (e.g., 'posts.create')
- `options` (object, optional):
  - `resourceParam` (string): URL parameter name for resource ID
  - `requireTenantOwnership` (boolean): Require tenant ownership validation
  - `useBouncerPolicy` (boolean): Use bouncer policy instead of role-based permissions

**Example:**

```typescript
@hasPermission('posts.create')
async store({ request, response }: HttpContext) {
  // Your controller logic
}

@hasPermission('posts.edit', { requireTenantOwnership: true })
async update({ params, request, response }: HttpContext) {
  // Only tenant owners can access this
}

@hasPermission('posts.view', { useBouncerPolicy: true, resourceParam: 'id' })
async show({ params, response }: HttpContext) {
  // Uses PostPolicy.view method with resource instance
}
```

### 2. `@hasAnyPermission(permissions, options)`

Checks if the authenticated user has ANY of the specified permissions.

**Parameters:**

- `permissions` (string[]): Array of permissions
- `options` (object, optional):
  - `requireTenantOwnership` (boolean): Require tenant ownership validation

**Example:**

```typescript
@hasAnyPermission(['posts.publish', 'posts.manage'])
async publish({ params, response }: HttpContext) {
  // User needs either posts.publish OR posts.manage permission
}
```

### 3. `@hasAllPermissions(permissions, options)`

Checks if the authenticated user has ALL of the specified permissions.

**Parameters:**

- `permissions` (string[]): Array of permissions
- `options` (object, optional):
  - `requireTenantOwnership` (boolean): Require tenant ownership validation

**Example:**

```typescript
@hasAllPermissions(['posts.manage', 'posts.bulk_edit'])
async bulkUpdate({ request, response }: HttpContext) {
  // User needs BOTH posts.manage AND posts.bulk_edit permissions
}
```

### 4. `@hasRole(roleName, options)`

Checks if the authenticated user has a specific role.

**Parameters:**

- `roleName` (string): Role name (e.g., 'tenant_owner', 'super_admin')
- `options` (object, optional):
  - `requireTenantContext` (boolean): Require tenant context for role checking

**Example:**

```typescript
@hasRole('super_admin')
async adminStats({ response }: HttpContext) {
  // Only super admins can access this
}

@hasRole('tenant_owner')
async moderate({ params, request, response }: HttpContext) {
  // Only tenant owners can moderate content
}
```

## How It Works

### 1. Authentication Check

All decorators first verify that the user is authenticated. If not, they return a 401 Unauthorized response.

### 2. Super Admin Bypass

Super admins automatically bypass all permission checks and can access any resource.

### 3. Tenant Context

The decorators extract tenant ID from the `x-tenant-id` header and set it in the TenantContext for proper multi-tenant isolation.

### 4. Permission Validation

Depending on the decorator type:

- **Role-based**: Uses `UserRoleService` to check permissions based on user roles
- **Policy-based**: Uses bouncer policies for more complex authorization logic

### 5. Tenant Ownership

When `requireTenantOwnership: true` is set, the decorator ensures the user either:

- Has the `tenant_owner` role for the specific tenant
- Has any role within the tenant (for other tenant-specific roles)

## Integration with Existing System

### Permission Middleware

The decorators work alongside your existing `PermissionMiddleware` and use the same `UserRoleService` for consistency.

### Bouncer Policies

When `useBouncerPolicy: true` is specified, the decorator:

1. Parses the permission string (e.g., 'posts.view' → resource: 'posts', action: 'view')
2. Dynamically imports the corresponding policy (e.g., `PostPolicy`)
3. Calls the policy method with the user and resource instance

### Tenant Context

The decorators automatically handle tenant context by:

1. Reading the `x-tenant-id` header
2. Setting the tenant ID in `TenantContext`
3. Passing the tenant ID to permission checks

## Usage Examples

### Basic CRUD Operations

```typescript
@inject()
export default class UsersController extends BaseController {
  @hasPermission('users.read')
  async index({ response }: HttpContext) {
    // List users
  }

  @hasPermission('users.create')
  async store({ request, response }: HttpContext) {
    // Create user
  }

  @hasPermission('users.update', { requireTenantOwnership: true })
  async update({ params, request, response }: HttpContext) {
    // Update user (tenant owners only)
  }

  @hasPermission('users.delete')
  async destroy({ params, response }: HttpContext) {
    // Delete user
  }
}
```

### Advanced Authorization

```typescript
@inject()
export default class AdminController extends BaseController {
  // Multiple permissions required
  @hasAllPermissions(['analytics.read', 'reports.generate'])
  async generateReport({ request, response }: HttpContext) {
    // Generate analytics report
  }

  // Any of the specified permissions
  @hasAnyPermission(['users.manage', 'users.view'])
  async listUsers({ response }: HttpContext) {
    // List users
  }

  // Role-based access
  @hasRole('super_admin')
  async systemSettings({ response }: HttpContext) {
    // System-wide settings
  }
}
```

### Bouncer Policy Integration

```typescript
@inject()
export default class UsersController extends BaseController {
  // Uses UserPolicy.view method
  @hasPermission('users.view', { useBouncerPolicy: true, resourceParam: 'id' })
  async show({ params, response }: HttpContext) {
    // The decorator will:
    // 1. Load the user with ID from params.id
    // 2. Call UserPolicy.view(user, targetUser)
    // 3. Allow/deny based on policy result
  }
}
```

## Error Responses

The decorators return standardized error responses:

### 401 Unauthorized

```json
{
  "success": false,
  "message": "Authentication required",
  "error": "User not authenticated"
}
```

### 403 Forbidden

```json
{
  "success": false,
  "message": "Access denied",
  "error": "Insufficient permissions. Required: posts.create"
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "message": "Permission check failed",
  "error": "Error details..."
}
```

## Best Practices

### 1. Use Appropriate Decorators

- Use `@hasPermission` for single permission checks
- Use `@hasAnyPermission` when multiple permissions could grant access
- Use `@hasAllPermissions` when multiple permissions are required
- Use `@hasRole` for role-based access control

### 2. Tenant Ownership

Always use `requireTenantOwnership: true` for tenant-specific resources that should only be accessible by tenant owners.

### 3. Bouncer Policies

Use `useBouncerPolicy: true` for complex authorization logic that goes beyond simple permission checking.

### 4. Error Handling

The decorators handle errors gracefully and return appropriate HTTP status codes. Your controller methods will only execute if authorization passes.

### 5. Performance

- Decorators cache permission checks within the same request
- Super admin bypass reduces unnecessary permission lookups
- Tenant context is set once per request

## Testing

When testing controllers with these decorators, you can:

1. **Mock the UserRoleService** to return specific permissions
2. **Set authentication context** in your test setup
3. **Provide tenant headers** for multi-tenant testing

```typescript
// Example test setup
test('should allow access with correct permission', async ({ client }) => {
  const user = await User.create(userData)
  const token = await User.accessTokens.create(user)

  const response = await client
    .get('/posts')
    .header('Authorization', `Bearer ${token.value!.release()}`)
    .header('x-tenant-id', '1')

  response.assertStatus(200)
})
```

## Troubleshooting

### Common Issues

1. **"User not authenticated"**: Ensure the auth middleware is applied before the controller
2. **"Tenant ID not set"**: Provide the `x-tenant-id` header in requests
3. **"Permission check failed"**: Verify the UserRoleService is properly injected
4. **Policy not found**: Ensure the policy file exists and is properly exported

### Debug Tips

1. Check the middleware order in your routes
2. Verify role and permission assignments in the database
3. Use logging to trace permission checks
4. Test with super admin users to isolate authorization issues

```

```
