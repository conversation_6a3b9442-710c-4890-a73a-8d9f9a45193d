import type { HttpContext } from '@adonisjs/core/http'

import UserRoleService from '#services/user_role_service'
import TenantContext from '#services/tenant_context'
import { HEADERS } from '#constants/headers'
import { extractTenant } from '#utils/extract_tenant'

/**
 * HasPermission decorator for SaaS RBAC system
 *
 * This decorator integrates with the existing permission middleware and bouncer policies
 * to check user permissions and roles within tenant context.
 *
 * @param permission - Permission string in format 'resource.action' (e.g., 'posts.create')
 * @param options - Additional options for permission checking
 */
export function hasPermission(
  permission: string,
  options: {
    resourceParam?: string
    requireAll?: boolean
    requireTenantOwnership?: boolean
  } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const ctx = args[0] as HttpContext
      const { auth, response, params, request } = ctx

      try {
        // Ensure user is authenticated
        if (!auth.user) {
          return response.unauthorized({
            success: false,
            message: 'Authentication required',
            error: 'User not authenticated',
          })
        }

        const user = auth.user

        const tenant = await extractTenant(ctx)

        if (!tenant) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: 'Tenant not found',
          })
        }
        TenantContext.setTenantId(tenant.id)
        ctx.request.tenant = tenant

        // Get UserRoleService instance
        const userRoleService = await ctx.containerResolver.make(UserRoleService)

        // Check if user is super admin (can access everything)
        const isSuperAdmin = await userRoleService.hasRole(user, 'super_admin', null)
        if (isSuperAdmin) {
          return originalMethod.apply(this, args)
        }

        // Handle tenant ownership requirement
        if (options.requireTenantOwnership && tenant.id) {
          const isTenantOwner = await userRoleService.hasRole(user, 'tenant_owner', tenant.id)
          if (!isTenantOwner) {
            // Check if user has any role in this tenant
            const userRoles = await userRoleService.getUserRoles(user, tenant.id)
            if (userRoles.length === 0) {
              return response.forbidden({
                success: false,
                message: 'Access denied',
                error: 'You can only access your own tenant resources',
              })
            }
          }
        }

        // Use role-based permission checking
        const hasPermission = await userRoleService.hasPermission(user, permission, tenant.id)

        if (!hasPermission) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: `Insufficient permissions. Required: ${permission}`,
          })
        }

        // Permission check passed, call original method
        return originalMethod.apply(this, args)
      } catch (error) {
        return response.forbidden({
          success: false,
          message: 'Permission check failed',
          error: error.message,
        })
      }
    }

    return descriptor
  }
}

/**
 * HasAnyPermission decorator - user must have ANY of the specified permissions
 */
export function hasAnyPermission(
  permissions: string[],
  options: {
    requireTenantOwnership?: boolean
  } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const ctx = args[0] as HttpContext
      const { auth, response, request } = ctx

      try {
        // Ensure user is authenticated
        if (!auth.user) {
          return response.unauthorized({
            success: false,
            message: 'Authentication required',
            error: 'User not authenticated',
          })
        }

        const user = auth.user

        // Get tenant ID from headers or context
        let tenantId: number | null = null
        const headerTenantId = request.header(HEADERS.TENANT_ID)
        if (headerTenantId) {
          tenantId = parseInt(headerTenantId)
          TenantContext.setTenantId(tenantId)
        }

        // Get UserRoleService instance
        const userRoleService = await ctx.containerResolver.make(UserRoleService)

        // Check if user is super admin (can access everything)
        const isSuperAdmin = await userRoleService.hasRole(user, 'super_admin', null)
        if (isSuperAdmin) {
          return originalMethod.apply(this, args)
        }

        // Handle tenant ownership requirement
        if (options.requireTenantOwnership && tenantId) {
          const isTenantOwner = await userRoleService.hasRole(user, 'tenant_owner', tenantId)
          if (!isTenantOwner) {
            // Check if user has any role in this tenant
            const userRoles = await userRoleService.getUserRoles(user, tenantId)
            if (userRoles.length === 0) {
              return response.forbidden({
                success: false,
                message: 'Access denied',
                error: 'You can only access your own tenant resources',
              })
            }
          }
        }

        // Check if user has any of the specified permissions
        const hasAnyPermission = await userRoleService.hasAnyPermission(user, permissions, tenantId)

        if (!hasAnyPermission) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: `Insufficient permissions. Required any of: ${permissions.join(', ')}`,
          })
        }

        // Permission check passed, call original method
        return originalMethod.apply(this, args)
      } catch (error) {
        return response.forbidden({
          success: false,
          message: 'Permission check failed',
          error: error.message,
        })
      }
    }

    return descriptor
  }
}

/**
 * HasAllPermissions decorator - user must have ALL of the specified permissions
 */
export function hasAllPermissions(
  permissions: string[],
  options: {
    requireTenantOwnership?: boolean
  } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const ctx = args[0] as HttpContext
      const { auth, response, request } = ctx

      try {
        // Ensure user is authenticated
        if (!auth.user) {
          return response.unauthorized({
            success: false,
            message: 'Authentication required',
            error: 'User not authenticated',
          })
        }

        const user = auth.user

        // Get tenant ID from headers or context
        let tenantId: number | null = null
        const headerTenantId = request.header(HEADERS.TENANT_ID)
        if (headerTenantId) {
          tenantId = parseInt(headerTenantId)
          TenantContext.setTenantId(tenantId)
        }

        // Get UserRoleService instance
        const userRoleService = await ctx.containerResolver.make(UserRoleService)

        // Check if user is super admin (can access everything)
        const isSuperAdmin = await userRoleService.hasRole(user, 'super_admin', null)
        if (isSuperAdmin) {
          return originalMethod.apply(this, args)
        }

        // Handle tenant ownership requirement
        if (options.requireTenantOwnership && tenantId) {
          const isTenantOwner = await userRoleService.hasRole(user, 'tenant_owner', tenantId)
          if (!isTenantOwner) {
            // Check if user has any role in this tenant
            const userRoles = await userRoleService.getUserRoles(user, tenantId)
            if (userRoles.length === 0) {
              return response.forbidden({
                success: false,
                message: 'Access denied',
                error: 'You can only access your own tenant resources',
              })
            }
          }
        }

        // Check if user has all of the specified permissions
        const hasAllPermissions = await userRoleService.hasAllPermissions(
          user,
          permissions,
          tenantId
        )

        if (!hasAllPermissions) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: `Insufficient permissions. Required all of: ${permissions.join(', ')}`,
          })
        }

        // Permission check passed, call original method
        return originalMethod.apply(this, args)
      } catch (error) {
        return response.forbidden({
          success: false,
          message: 'Permission check failed',
          error: error.message,
        })
      }
    }

    return descriptor
  }
}

/**
 * HasRole decorator - user must have the specified role
 */
export function hasRole(
  roleName: string,
  options: {
    requireTenantContext?: boolean
  } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const ctx = args[0] as HttpContext
      const { auth, response, request } = ctx

      try {
        // Ensure user is authenticated
        if (!auth.user) {
          return response.unauthorized({
            success: false,
            message: 'Authentication required',
            error: 'User not authenticated',
          })
        }

        const user = auth.user

        // Get tenant ID from headers or context
        let tenantId: number | null = null
        const headerTenantId = request.header(HEADERS.TENANT_ID)
        if (headerTenantId) {
          tenantId = parseInt(headerTenantId)
          TenantContext.setTenantId(tenantId)
        }

        // Get UserRoleService instance
        const userRoleService = await ctx.containerResolver.make(UserRoleService)

        // Check if user has the specified role
        const hasRole = await userRoleService.hasRole(user, roleName, tenantId)

        if (!hasRole) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: `Insufficient role. Required: ${roleName}`,
          })
        }

        // Role check passed, call original method
        return originalMethod.apply(this, args)
      } catch (error) {
        return response.internalServerError({
          success: false,
          message: 'Role check failed',
          error: error.message,
        })
      }
    }

    return descriptor
  }
}
