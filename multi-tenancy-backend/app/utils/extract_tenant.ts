import { HEADERS } from '#constants/headers'
import Tenant from '#models/tenant'
import { HttpContext } from '@adonisjs/core/http'

export async function extractTenant(ctx: HttpContext): Promise<Tenant | null> {
  const { request } = ctx

  // Get tenant ID from headers (for tenant context switching)
  const headerTenantId = request.header(HEADERS.TENANT_ID)
  if (headerTenantId) {
    const tenant = await Tenant.find(parseInt(headerTenantId))
    return tenant
  }

  return null
}
