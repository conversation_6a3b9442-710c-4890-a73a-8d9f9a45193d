import vine from '@vinejs/vine'
import { FieldContext } from '@vinejs/vine/types'

/**
 * Common validation patterns and utilities
 */

/**
 * Standard name validation (2-100 characters)
 */
export const nameValidation = vine.string().trim().minLength(2).maxLength(100)

/**
 * Standard slug validation (2-50 characters, lowercase alphanumeric with hyphens)
 */
export const slugValidation = vine
  .string()
  .trim()
  .minLength(2)
  .maxLength(50)
  .regex(/^[a-z0-9-]+$/)

/**
 * Standard email validation with normalization
 */
export const emailValidation = vine.string().trim().email().normalizeEmail()

/**
 * Standard phone validation (optional)
 */
export const phoneValidation = vine.string().trim().optional()

/**
 * Standard description validation (optional, max 255 characters)
 */
export const descriptionValidation = vine.string().trim().maxLength(255).optional()

/**
 * Standard display name validation (2-100 characters)
 */
export const displayNameValidation = vine.string().trim().minLength(2).maxLength(100)

/**
 * Custom validation rule for password confirmation
 */
function passwordConfirmation(value: unknown, _options: undefined, field: FieldContext) {
  if (typeof value !== 'string') {
    return
  }

  // Get the parent data to access both password fields
  const data = field.data as any

  if (data.newPassword !== data.confirmPassword) {
    field.report('The {{ field }} field must match the new password', 'passwordConfirmation', field)
  }
}

/**
 * Convert the function to a VineJS rule
 */
export const passwordConfirmationRule = vine.createRule(passwordConfirmation)

/**
 * Standard password validation
 */
export const passwordValidation = vine.string().minLength(8).maxLength(255)

/**
 * Standard ID validation for route parameters
 */
export const idValidation = vine.number().positive()

/**
 * Standard array of IDs validation
 */
export const idsValidation = vine.array(vine.number().positive())

/**
 * Standard date validation
 */
export const dateValidation = vine.date()

/**
 * Standard optional date validation
 */
export const optionalDateValidation = vine.date().optional()

/**
 * Standard pagination validation
 */
export const paginationValidation = {
  page: vine.number().positive().optional(),
  limit: vine.number().positive().max(100).optional(),
}

/**
 * Standard search validation
 */
export const searchValidation = vine.string().trim().maxLength(255).optional()

/**
 * Create unique validation for a specific table and column
 */
export const createUniqueValidation = (table: string, column: string, ignoreId?: number) => {
  const validation = vine.string().unique({ table, column })
  
  if (ignoreId) {
    return validation.unique({ table, column, whereNot: { id: ignoreId } })
  }
  
  return validation
}
