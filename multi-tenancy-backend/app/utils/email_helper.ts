/**
 * Email utility functions for normalization and comparison
 */

/**
 * Normalize an email address for comparison purposes
 * This follows the same normalization rules as Vine.js normalizeEmail()
 * 
 * @param email - The email address to normalize
 * @returns The normalized email address
 */
export function normalizeEmail(email: string): string {
  if (!email || typeof email !== 'string') {
    return email
  }

  // Split email into local and domain parts
  const [localPart, domain] = email.toLowerCase().split('@')
  
  if (!localPart || !domain) {
    return email.toLowerCase()
  }

  let normalizedLocal = localPart

  // Gmail-specific normalization
  if (domain === 'gmail.com' || domain === 'googlemail.com') {
    // Remove dots from Gmail addresses
    normalizedLocal = localPart.replace(/\./g, '')
    
    // Remove everything after + (plus addressing)
    const plusIndex = normalizedLocal.indexOf('+')
    if (plusIndex !== -1) {
      normalizedLocal = normalizedLocal.substring(0, plusIndex)
    }
  }

  return `${normalizedLocal}@${domain}`
}

/**
 * Compare two email addresses using normalized comparison
 * 
 * @param email1 - First email address
 * @param email2 - Second email address
 * @returns True if emails are equivalent when normalized
 */
export function emailsMatch(email1: string, email2: string): boolean {
  if (!email1 || !email2) {
    return false
  }
  
  return normalizeEmail(email1) === normalizeEmail(email2)
}

/**
 * Check if an email exists in a list of emails using normalized comparison
 * 
 * @param targetEmail - The email to search for
 * @param emailList - Array of emails to search in
 * @returns True if the email exists in the list (normalized comparison)
 */
export function emailExistsInList(targetEmail: string, emailList: string[]): boolean {
  if (!targetEmail || !emailList || emailList.length === 0) {
    return false
  }
  
  const normalizedTarget = normalizeEmail(targetEmail)
  return emailList.some(email => normalizeEmail(email) === normalizedTarget)
}
