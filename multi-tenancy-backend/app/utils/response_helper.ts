export interface ApiSuccessResponse<T = any> {
  success: true
  message: string
  data: T
  [key: string]: any // For additional meta properties like pagination
}

export interface ApiErrorResponse {
  success: false
  message: string
  error: string
}

export class ResponseHelper {
  /**
   * Create a standardized success response
   */
  static success<T>(
    response: any,
    data: T,
    message: string,
    meta: Record<string, any> = {},
    statusCode = 200
  ) {
    const responseData: ApiSuccessResponse<T> = {
      success: true,
      message,
      data,
      ...meta,
    }

    return response.status(statusCode).json(responseData)
  }

  /**
   * Create a standardized error response
   */
  static error(response: any, message: string, error: string, statusCode = 400) {
    const responseData: ApiErrorResponse = {
      success: false,
      message,
      error,
    }

    return response.status(statusCode).json(responseData)
  }

  /**
   * Create a paginated success response
   */
  static paginated<T>(
    response: any,
    data: T,
    message: string,
    pagination: {
      currentPage: number
      perPage: number
      total: number
      lastPage: number
      hasMorePages: boolean
      hasPages: boolean
    },
    statusCode = 200
  ) {
    return this.success(response, data, message, { pagination }, statusCode)
  }

  /**
   * Handle common error types and return appropriate status codes
   */
  static handleError(response: any, error: Error, defaultMessage = 'Operation failed') {
    let statusCode = 500
    let message = defaultMessage

    if (error.message.includes('not found')) {
      statusCode = 404
      message = 'Resource not found'
    } else if (error.message.includes('permission') || error.message.includes('access')) {
      statusCode = 403
      message = 'Access denied'
    } else if (error.message.includes('validation')) {
      statusCode = 400
      message = 'Validation failed'
    } else if (error.message.includes('unauthorized')) {
      statusCode = 401
      message = 'Unauthorized'
    }

    return this.error(response, message, error.message, statusCode)
  }
}
