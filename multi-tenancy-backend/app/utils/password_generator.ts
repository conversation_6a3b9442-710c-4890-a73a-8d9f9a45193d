import { randomBytes } from 'node:crypto'

/**
 * Generate a secure random password
 */
export function generateSecurePassword(length: number = 12): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  const charsetLength = charset.length
  const randomBytesArray = randomBytes(length)
  
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset[randomBytesArray[i] % charsetLength]
  }
  
  return password
}

/**
 * Generate a user-friendly password (no special characters)
 */
export function generateFriendlyPassword(length: number = 10): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charsetLength = charset.length
  const randomBytesArray = randomBytes(length)
  
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset[randomBytesArray[i] % charsetLength]
  }
  
  return password
}

/**
 * Generate a memorable password with words and numbers
 */
export function generateMemorablePassword(): string {
  const adjectives = ['Quick', 'Bright', 'Swift', 'Smart', 'Bold', 'Calm', 'Cool', 'Fast']
  const nouns = ['Tiger', 'Eagle', 'River', 'Mountain', 'Ocean', 'Forest', 'Star', 'Moon']
  const numbers = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  
  return `${adjective}${noun}${numbers}`
}
