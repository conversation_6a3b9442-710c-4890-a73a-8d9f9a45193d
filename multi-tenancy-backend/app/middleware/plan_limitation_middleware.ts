import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { inject } from '@adonisjs/core'
import { PlanLimitationAction } from '#enums/plan_limitation'
import PlanLimitationService from '#services/plan_limitation_service'
import { extractTenant } from '#utils/tenant_helper'

export interface PlanLimitationOptions {
  action: PlanLimitationAction
  quantity?: number
  skipSuperAdmin?: boolean
}

@inject()
export default class PlanLimitationMiddleware {
  constructor(private planLimitationService: PlanLimitationService) {}

  /**
   * Handle the request and check plan limitations
   */
  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: PlanLimitationOptions
  ) {
    const { action, quantity = 1, skipSuperAdmin = true } = options
    const { auth, response } = ctx

    // Ensure user is authenticated
    if (!auth.user) {
      return response.unauthorized({
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      })
    }

    try {
      // Get tenant context
      const tenant = await extractTenant(ctx)
      if (!tenant) {
        return response.forbidden({
          success: false,
          message: 'Access denied',
          error: 'Tenant not found',
        })
      }

      // Skip plan limitation check for super admin if specified
      if (skipSuperAdmin) {
        // Check if user is super admin (you may need to adjust this based on your role service)
        // For now, we'll assume you have a way to check this
        const userRoles = await auth.user.related('userRoles').query()
          .preload('role')
          .where('tenant_id', null) // Super admin roles typically have null tenant_id
        
        const isSuperAdmin = userRoles.some(userRole => 
          userRole.role && userRole.role.name === 'super_admin'
        )

        if (isSuperAdmin) {
          return next()
        }
      }

      // Check plan limitation
      const limitationResult = await this.planLimitationService.checkActionAllowed(
        tenant.id,
        action,
        quantity
      )

      if (!limitationResult.allowed) {
        return response.forbidden({
          success: false,
          message: 'Plan limitation exceeded',
          error: limitationResult.message || `Action '${action}' not allowed due to plan limitations`,
          data: {
            currentUsage: limitationResult.currentUsage,
            limit: limitationResult.limit,
            remaining: limitationResult.remaining,
            isUnlimited: limitationResult.isUnlimited,
            action,
            quantity,
          },
        })
      }

      // Plan limitation check passed, continue to next middleware/controller
      return next()
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Plan limitation check failed',
        error: error.message,
      })
    }
  }
}
