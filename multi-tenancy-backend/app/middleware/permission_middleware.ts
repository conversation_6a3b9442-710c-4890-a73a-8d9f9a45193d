import TenantContext from '#services/tenant_context'
import UserRoleService from '#services/user_role_service'
import { HEADERS } from '#constants/headers'
import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { extractTenant } from '#utils/extract_tenant'

/**
 * Permission middleware for SaaS RBAC system
 *
 * Role Hierarchy:
 * - super_admin: Can control everything, including tenant management
 * - tenant_owner: Can manage their own tenant and business logic within it
 * - tenant_user: Can access business logic within their tenant (future)
 * - staff: Limited access within tenant (future)
 */
@inject()
export default class PermissionMiddleware {
  constructor(protected userRoleService: UserRoleService) {}

  /**
   * Handle the request and check permissions
   */
  async handle(
    ctx: HttpContext,
    next: NextFn,
    options: {
      permissions?: string[]
      requireAll?: boolean
      requireTenantOwnership?: boolean
    } = {}
  ) {
    const { permissions = [], requireAll = false, requireTenantOwnership = false } = options

    const { auth, response } = ctx

    // Ensure user is authenticated
    if (!auth.user) {
      return response.unauthorized({
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      })
    }

    const user = auth.user

    // If no permissions specified, just continue (auth check passed)
    if (permissions.length === 0) {
      return next()
    }

    try {
      // Check if user is super admin
      const isSuperAdmin = await this.userRoleService.hasRole(user, 'super_admin', null)
      // Get tenant context from request or user

      // Try to get tenant ID from headers
      const tenant = await extractTenant(ctx)
      if (!tenant) {
        return response.forbidden({
          success: false,
          message: 'Access denied',
          error: 'Tenant not found',
        })
      }
      TenantContext.setTenantId(tenant.id)
      ctx.request.tenant = tenant
      // Super admin can access everything
      if (isSuperAdmin) {
        return next()
      }

      // Special handling for tenant ownership requirements
      if (requireTenantOwnership) {
        const hasOwnership = await this.validateTenantOwnership(user, tenant.id)
        if (!hasOwnership) {
          return response.forbidden({
            success: false,
            message: 'Access denied',
            error: 'You can only access your own tenant resources',
          })
        }
      }

      // Check permissions based on role and tenant context
      let hasPermission = false

      if (requireAll) {
        // User must have ALL specified permissions
        hasPermission = await this.userRoleService.hasAllPermissions(user, permissions, tenant.id)
      } else {
        // User must have ANY of the specified permissions
        hasPermission = await this.userRoleService.hasAnyPermission(user, permissions, tenant.id)
      }

      if (!hasPermission) {
        return response.forbidden({
          success: false,
          message: 'Access denied',
          error: `Insufficient permissions. Required: ${permissions.join(', ')}`,
        })
      }

      // Permission check passed, continue to next middleware/controller
      return next()
    } catch (error) {
      return response.forbidden({
        success: false,
        message: 'Permission check failed',
        error: error.message,
      })
    }
  }

  /**
   * Validate that user owns or has access to the specified tenant
   */
  private async validateTenantOwnership(user: any, tenantId: number | null): Promise<boolean> {
    if (tenantId === null) {
      return false
    }

    // Check if user is tenant owner for this specific tenant
    const isTenantOwner = await this.userRoleService.hasRole(user, 'tenant_owner', tenantId)

    if (isTenantOwner) {
      return true
    }

    // Check if user has any role in this tenant (for future roles like staff)
    const userRoles = await this.userRoleService.getUserRoles(user, tenantId)

    return userRoles.length > 0
  }
}
