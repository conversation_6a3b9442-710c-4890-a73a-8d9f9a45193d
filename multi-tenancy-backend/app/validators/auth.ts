import vine from '@vinejs/vine'
import { UserRole } from '#models/user'
import {
  emailValidation,
  passwordValidation,
  passwordConfirmationRule,
  phoneValidation,
} from '#utils/validation'

/**
 * Validator for user registration
 */
export const registerValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(2).maxLength(50),
    lastName: vine.string().trim().minLength(2).maxLength(50),
    email: emailValidation,
    password: passwordValidation,
    phone: phoneValidation,
    role: vine.enum(Object.values(UserRole)).optional(),
    tenantId: vine.number().positive().optional(),
  })
)

/**
 * Validator for user login
 */
export const loginValidator = vine.compile(
  vine.object({
    email: emailValidation,
    password: vine.string().minLength(1),
  })
)

/**
 * Validator for updating user profile
 */
export const updateProfileValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(2).maxLength(50).optional(),
    lastName: vine.string().trim().minLength(2).maxLength(50).optional(),
    phone: phoneValidation,
  })
)

/**
 * Validator for changing password
 */
export const changePasswordValidator = vine.compile(
  vine.object({
    currentPassword: vine.string().minLength(1),
    newPassword: passwordValidation,
    confirmPassword: vine.string().minLength(1).use(passwordConfirmationRule()),
  })
)
