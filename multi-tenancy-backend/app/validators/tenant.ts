import vine from '@vinejs/vine'
import { TenantStatus } from '#models/tenant'
import { nameValidation, slugValidation, optionalDateValidation } from '#utils/validation'

/**
 * Validator for creating a new tenant
 */
export const createTenantValidator = vine.compile(
  vine.object({
    name: nameValidation,
    slug: slugValidation.unique({
      table: 'tenants',
      column: 'slug',
    }),
    status: vine.enum(Object.values(TenantStatus)).optional(),
    trialEndsAt: optionalDateValidation,
  })
)

/**
 * Validator for creating a tenant with auto-generated slug (simplified creation)
 */
export const createTenantSimpleValidator = vine.compile(
  vine.object({
    name: nameValidation,
  })
)

/**
 * Validator for updating a tenant
 */
export const updateTenantValidator = vine.compile(
  vine.object({
    name: nameValidation.optional(),
    slug: slugValidation
      .unique({
        table: 'tenants',
        column: 'slug',
      })
      .optional(),
    status: vine.enum(Object.values(TenantStatus)).optional(),
    trialEndsAt: optionalDateValidation,
  })
)

/**
 * Validator for updating tenant status
 */
export const updateTenantStatusValidator = vine.compile(
  vine.object({
    status: vine.enum(Object.values(TenantStatus)),
  })
)
