import vine from '@vinejs/vine'
import { UserStatus } from '#models/user'
import {
  emailValidation,
  phoneValidation,
  passwordValidation,
  idValidation,
} from '#utils/validation'

/**
 * Validator to validate the payload when creating a new user
 */
export const createUserValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(1).maxLength(50),
    lastName: vine.string().trim().minLength(1).maxLength(50),
    email: emailValidation,
    password: passwordValidation,
    phone: phoneValidation,
    status: vine.enum(Object.values(UserStatus)).optional(),
    roleId: idValidation,
  })
)

/**
 * Validator to validate the payload when updating a user
 */
export const updateUserValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(1).maxLength(50).optional(),
    lastName: vine.string().trim().minLength(1).maxLength(50).optional(),
    email: emailValidation.optional(),
    phone: phoneValidation,
    status: vine.enum(Object.values(UserStatus)).optional(),
    password: passwordValidation.optional(),
  })
)

/**
 * Validator to validate the payload when updating user status
 */
export const updateUserStatusValidator = vine.compile(
  vine.object({
    status: vine.enum(['active', 'inactive', 'suspended']),
  })
)

/**
 * Validator to validate the payload when inviting a user
 */
export const inviteUserValidator = vine.compile(
  vine.object({
    firstName: vine.string().trim().minLength(1).maxLength(50),
    lastName: vine.string().trim().minLength(1).maxLength(50),
    email: vine.string().trim().email(),
    phone: vine.string().trim().optional(),
  })
)

/**
 * Validator to validate the payload when bulk inviting users
 */
export const bulkInviteUsersValidator = vine.compile(
  vine.object({
    users: vine.array(
      vine.object({
        firstName: vine.string().trim().minLength(1).maxLength(50),
        lastName: vine.string().trim().minLength(1).maxLength(50),
        email: vine.string().trim().email(),
        phone: vine.string().trim().optional(),
      })
    ),
  })
)
