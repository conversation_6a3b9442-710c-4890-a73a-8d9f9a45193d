import vine from '@vinejs/vine'
import { displayNameValidation, descriptionValidation, idsValidation } from '#utils/validation'

/**
 * Validator for creating a new role
 */
export const createRoleValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(2).maxLength(50),
    displayName: displayNameValidation,
    description: descriptionValidation,
    permissionIds: idsValidation.optional(),
  })
)

/**
 * Validator for updating a role
 */
export const updateRoleValidator = vine.compile(
  vine.object({
    name: vine.string().trim().minLength(2).maxLength(50).optional(),
    displayName: displayNameValidation.optional(),
    description: descriptionValidation,
    permissionIds: idsValidation.optional(),
  })
)

/**
 * Validator for assigning permissions to a role
 */
export const assignPermissionsValidator = vine.compile(
  vine.object({
    permissionIds: idsValidation,
  })
)
