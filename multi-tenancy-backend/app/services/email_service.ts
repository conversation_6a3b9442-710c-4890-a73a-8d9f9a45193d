/**
 * Email Service for Multi-Tenant SaaS Application
 *
 * Environment Variables Required:
 * - MAIL_DOMAIN: The domain for tenant-specific email addresses (e.g., "company.com")
 *   This will generate emails like: <EMAIL>, <EMAIL>
 * - MAIL_SUPPORT_ADDRESS: Centralized support email address (e.g., "<EMAIL>")
 *   Used in email templates for customer support contact information
 *
 * Email Address Format:
 * - Sender: Uses tenant slug as the local part: {tenant.slug}@{MAIL_DOMAIN}
 * - Support: Uses centralized support address: MAIL_SUPPORT_ADDRESS
 * - Falls back to sanitized tenant name if slug is not available
 */

import { inject } from '@adonisjs/core'
import mail from '@adonisjs/mail/services/main'
import User from '#models/user'
import Tenant from '#models/tenant'

export interface EmailRetryOptions {
  maxRetries?: number
  retryDelay?: number // in milliseconds
}

export interface UserInvitationEmailData {
  tenantName: string
  temporaryPassword: string
}

@inject()
export default class EmailService {
  /**
   * Send user invitation email
   */
  async sendUserInvitation(user: User, tenant: Tenant, temporaryPassword: string): Promise<void> {
    const fromEmail = this.getTenantEmailAddress(tenant)
    const fromName = tenant.name

    await mail.sendLater((message) => {
      message
        .to(user.email)
        .from(fromEmail, fromName)
        .subject(`Invitation to join ${tenant.name}`)
        .htmlView('emails/user_invitation', {
          tenantName: tenant.name,
          tenantSlug: tenant.slug,
          temporaryPassword,
        })
    })
  }

  /**
   * Generate tenant-specific email address
   * Format: <EMAIL>
   */
  private getTenantEmailAddress(tenant: Tenant): string {
    const domain = process.env.MAIL_DOMAIN || 'company.com'
    const tenantSlug = tenant.slug || tenant.name.toLowerCase().replace(/\s+/g, '-')
    return `${tenantSlug}@${domain}`
  }

  /**
   * Send password reset email (placeholder for future implementation)
   */
  async sendPasswordReset(user: User, tenant: Tenant, resetToken: string): Promise<void> {
    const fromEmail = this.getTenantEmailAddress(tenant)
    const fromName = tenant.name

    // TODO: Implement password reset email template
    await mail.sendLater((message) => {
      message
        .to(user.email)
        .from(fromEmail, fromName)
        .subject(`Reset your password for ${tenant.name}`)
        .htmlView('emails/password_reset', {
          tenantName: tenant.name,
          resetToken,
          user: user.displayName,
        })
    })
  }

  /**
   * Send welcome email (placeholder for future implementation)
   */
  async sendWelcomeEmail(user: User, tenant: Tenant): Promise<void> {
    const fromEmail = this.getTenantEmailAddress(tenant)
    const fromName = tenant.name

    // TODO: Implement welcome email template
    await mail.sendLater((message) => {
      message
        .to(user.email)
        .from(fromEmail, fromName)
        .subject(`Welcome to ${tenant.name}!`)
        .htmlView('emails/welcome', {
          tenantName: tenant.name,
          user: user.displayName,
        })
    })
  }

  /**
   * Send notification email (placeholder for future implementation)
   */
  async sendNotification(
    user: User,
    tenant: Tenant,
    subject: string,
    content: string
  ): Promise<void> {
    const fromEmail = this.getTenantEmailAddress(tenant)
    const fromName = tenant.name

    // TODO: Implement notification email template
    await mail.sendLater((message) => {
      message
        .to(user.email)
        .from(fromEmail, fromName)
        .subject(subject)
        .htmlView('emails/notification', {
          tenantName: tenant.name,
          user: user.displayName,
          content,
        })
    })
  }
}
