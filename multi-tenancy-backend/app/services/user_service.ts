import { inject } from '@adonisjs/core'
import User, { UserStatus } from '#models/user'
import BaseService from '#services/base_service'
import { BaseFilters } from '../types/service.js'

import UserRepository, { CreateUserData, UpdateUserData } from '#repositories/user_repository'

import BadRequestException from '#exceptions/bad_request_exception'
import Role from '#models/role'
import { Role as RoleEnum } from '#enums/role'
import { generateFriendlyPassword } from '#utils/password_generator'
import UserRoleRepository from '#repositories/user_role_repository'
import UserPermissionRepository from '#repositories/user_permission_repository'
import { BasePaginationFilters, BasePaginationResult } from '#repositories/base_repository'

export interface UserFilters extends BaseFilters {
  status?: UserStatus
}

export interface InviteUserData {
  email: string
  roleId: number
  tenantId: number
}

export interface CreateUserWithRoleData {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  status?: UserStatus
  roleId?: number
  tenantId?: number
}

export interface UpdateUserPermissionsData {
  userId: number
  permissionIds: number[]
  tenantId?: number | null
}

@inject()
export default class UserService extends BaseService<typeof User> {
  protected model = User

  constructor(
    protected repository: UserRepository,
    private userRoleRepository: UserRoleRepository,
    private userPermissionRepository: UserPermissionRepository
  ) {
    super()
  }

  async getPaginated(
    filters: UserFilters = {},
    tenantId?: number
  ): Promise<BasePaginationResult<User>> {
    return await this.repository.getPaginated(filters, tenantId)
  }

  /**
   * Find user by ID
   */
  async findById(id: number, tenantId?: number): Promise<User | null> {
    if (tenantId) {
      this.repository.filterTenant(tenantId)
    }

    return await this.repository.findById(id)
  }

  /**
   * Get a user by email using normalized comparison
   */
  async getUserByEmail(email: string): Promise<User | null> {
    return await this.repository.findByEmail(email)
  }

  /**
   * Create a new user (domain operation only)
   */
  async createUser(data: CreateUserData): Promise<User> {
    // Check if email already exists using normalized comparison
    const existingUser = await this.repository.findByEmail(data.email)

    if (existingUser) {
      throw new BadRequestException('User with this email already exists')
    }

    // Create user using repository
    const user = await this.repository.create({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      password: data.password,
      phone: data.phone,
      status: data.status,
    })

    return user
  }

  /**
   * Update user status
   */
  async updateUserStatus(id: number, status: UserStatus): Promise<User> {
    return await this.repository.update(id, { status })
  }

  async invite(data: InviteUserData): Promise<{ user: User; temporaryPassword: string }> {
    const role = await Role.find(data.roleId)
    if (role && role.name === RoleEnum.SUPER_ADMIN) {
      throw new BadRequestException('Cannot invite users with super_admin role')
    }
    // Check if user already exists globally
    let user = await this.repository.findByEmail(data.email)
    let temporaryPassword = ''

    if (user) {
      // Check if user already exists in the same tenant
      const existsInTenant = await this.repository.existsInTenant(data.email, data.tenantId)

      if (existsInTenant) {
        throw new BadRequestException(`User with email ${data.email} already exists in this tenant`)
      }
    } else {
      // Generate a secure temporary password
      temporaryPassword = generateFriendlyPassword(12)

      // Create new user through domain service
      user = await this.createUser({
        email: data.email,
        firstName: '',
        lastName: '',
        password: temporaryPassword,
      })
    }

    return {
      user,
      temporaryPassword,
    }
  }

  async assignRoleAndPermissions(
    user: User,
    roleId: number,
    tenantId: number,
    permissions?: string[]
  ): Promise<void> {
    // Assign role to user
    await this.userRoleRepository.assignRole(user.id, roleId, tenantId)

    // Assign permissions if provided
    if (permissions && permissions.length > 0) {
      for (const permissionName of permissions) {
        await this.userPermissionRepository.assignPermissionByName(
          user.id,
          permissionName,
          tenantId
        )
      }
    }
  }

  /**
   * Update user permissions
   */
  async updateUserPermissions(data: UpdateUserPermissionsData): Promise<void> {
    const user = await this.repository.findByIdOrFail(data.userId)

    // Check if user is super admin or tenant owner (they shouldn't use direct permissions)
    const isSuperAdmin = await this.userRoleRepository.hasUserRole(user.id, 'super_admin', null)
    const isTenantOwner = data.tenantId
      ? await this.userRoleRepository.hasUserRole(user.id, 'tenant_owner', data.tenantId)
      : false

    if (isSuperAdmin || isTenantOwner) {
      throw new BadRequestException(
        'Cannot assign direct permissions to super admin or tenant owner'
      )
    }

    // Remove existing permissions for this user in this tenant
    await this.userPermissionRepository.removeAllUserPermissions(user.id, data.tenantId)

    // Assign new permissions
    if (data.permissionIds.length > 0) {
      for (const permissionId of data.permissionIds) {
        await this.userPermissionRepository.assignPermission(user.id, permissionId, data.tenantId)
      }
    }
  }

  /**
   * Delete a user from a tenant (removes user_roles record for this tenant)
   */
  async removeUserFromTenant(userId: number, tenantId: number | null): Promise<void> {
    const user = await this.repository.findByIdOrFail(userId)

    // Remove all roles for this user within the tenant
    await this.userRoleRepository.removeAllUserRoles(user.id, tenantId)
  }

  /**
   * Get user permissions for a tenant
   */
  async getUserPermissions(userId: number, tenantId?: number | null): Promise<string[]> {
    // Get permissions from roles
    const rolePermissions = await this.userRoleRepository.getUserPermissions(userId, tenantId)

    // Get direct permissions
    const directPermissions = await this.userPermissionRepository.getUserDirectPermissions(
      userId,
      tenantId
    )

    // Combine and deduplicate
    const allPermissions = [...rolePermissions, ...directPermissions]
    return [...new Set(allPermissions)]
  }
}
