import BaseRepository, {
  BasePaginationFilters,
  BasePaginationResult,
} from '#repositories/base_repository'
import { inject } from '@adonisjs/core'
import { BaseModel } from '@adonisjs/lucid/orm'

@inject()
export default abstract class BaseService<T extends typeof BaseModel> {
  protected abstract model: T
  protected abstract repository: BaseRepository<T>

  async getPaginated(
    filters: BasePaginationFilters & Record<string, any>
  ): Promise<BasePaginationResult<InstanceType<T>>> {
    return await this.repository.getPaginated(filters)
  }
}
