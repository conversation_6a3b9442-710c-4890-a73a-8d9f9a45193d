import { inject } from '@adonisjs/core'
import User from '#models/user'
import UserRole from '#models/user_role'
import BaseService from '#services/base_service'
import UserPermissionService from '#services/user_permission_service'
import RolePermissionService from '#services/role_permission_service'
import { Role as RoleEnum } from '#enums/role'
import UserRoleRepository from '#repositories/user_role_repository'
import PermissionRepository from '#repositories/permission_repository'
import RoleRepository from '#repositories/role_repository'

@inject()
export default class UserRoleService extends BaseService<typeof UserRole> {
  protected model = UserRole
  protected repository: UserRoleRepository

  constructor(
    private userPermissionService: UserPermissionService,
    private rolePermissionService: RolePermissionService,
    userRoleRepository: UserRoleRepository,
    private permissionRepository: PermissionRepository,
    private roleRepository: RoleRepository
  ) {
    super()
    this.repository = userRoleRepository
  }

  /**
   * Get all roles for a user (optionally filtered by tenant)
   */
  async getUserRoles(user: User, tenantId?: number | null): Promise<any[]> {
    return await this.repository.getUserRolesWithDetails(user, tenantId)
  }

  /**
   * Get all permissions for a user (optionally filtered by tenant)
   * Super admin and tenant owner use role-based permissions
   * Other users use direct user permissions
   */
  async getUserPermissions(user: User, tenantId?: number | null): Promise<string[]> {
    const isSuperAdmin = await this.hasRole(user, RoleEnum.SUPER_ADMIN, null)

    const isTenantOwner = await this.hasRole(user, RoleEnum.TENANT_OWNER, tenantId)

    if (isSuperAdmin) {
      return await this.permissionRepository.getAllPermissionNames()
    }

    if (isTenantOwner) {
      return await this.rolePermissionService.getRolePermissions(RoleEnum.TENANT_OWNER)
    }

    return await this.userPermissionService.getUserDirectPermissions(user, tenantId)
  }

  /**
   * Check if user has a specific permission (optionally within a tenant)
   */
  async hasPermission(
    user: User,
    permissionName: string,
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserPermissions(user, tenantId)
    return permissions.includes(permissionName)
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(
    user: User,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserPermissions(user, tenantId)
    return permissionNames.some((name) => permissions.includes(name))
  }

  /**
   * Check if user has all of the specified permissions
   */
  async hasAllPermissions(
    user: User,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserPermissions(user, tenantId)
    return permissionNames.every((name) => permissions.includes(name))
  }

  /**
   * Check if user has a specific role (optionally within a tenant)
   */
  async hasRole(user: User, roleName: string, tenantId?: number | null): Promise<boolean> {
    return await this.repository.hasUserRole(user.id, roleName, tenantId)
  }

  /**
   * Assign a role to the user (optionally within a tenant)
   */
  async assignRole(user: User, roleId: number, tenantId?: number | null): Promise<void> {
    // Check if the role is super_admin and prevent assignment
    const role = await this.roleRepository.findById(roleId)
    if (role && role.name === 'super_admin') {
      throw new Error('Cannot assign super_admin role through this method')
    }

    // Check if already assigned
    const existing = await this.repository.findExistingAssignment(user.id, roleId, tenantId)

    if (!existing) {
      await this.repository.create({
        userId: user.id,
        roleId,
        tenantId,
      })
    }
  }

  /**
   * Remove a role from the user (optionally within a tenant)
   */
  async removeRole(user: User, roleId: number, tenantId?: number | null): Promise<void> {
    await this.repository.removeUserRole(user.id, roleId, tenantId)
  }

  /**
   * Assign a role by name to the user (optionally within a tenant)
   */
  async assignRoleByName(user: User, roleName: string, tenantId?: number | null): Promise<void> {
    const role = await this.roleRepository.findByName(roleName)
    if (role) {
      await this.assignRole(user, role.id, tenantId)
    }
  }

  /**
   * Remove a role by name from the user (optionally within a tenant)
   */
  async removeRoleByName(user: User, roleName: string, tenantId?: number | null): Promise<void> {
    const role = await this.roleRepository.findByName(roleName)
    if (role) {
      await this.removeRole(user, role.id, tenantId)
    }
  }

  /**
   * Get user roles and permissions formatted for API responses
   */
  async getUserRolesAndPermissions(user: User, tenantId?: number | null) {
    const roles = await this.getUserRoles(user, tenantId)

    const permissions = await this.getUserPermissions(user, tenantId)

    return {
      roles: roles.map((role: any) => ({
        id: role.id,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
      })),
      permissions,
    }
  }

  /**
   * Bulk assign roles to a user
   */
  async bulkAssignRoles(
    user: User,
    roleAssignments: Array<{ roleId: number; tenantId?: number | null }>
  ): Promise<void> {
    // Check for super_admin roles and filter them out
    const validAssignments = []
    for (const assignment of roleAssignments) {
      const role = await this.roleRepository.findById(assignment.roleId)
      if (role && role.name !== 'super_admin') {
        validAssignments.push({
          userId: user.id,
          roleId: assignment.roleId,
          tenantId: assignment.tenantId,
        })
      }
    }

    if (validAssignments.length > 0) {
      await this.repository.bulkCreateUserRoles(validAssignments)
    }
  }

  /**
   * Remove all roles from a user (optionally within a specific tenant)
   */
  async removeAllRoles(user: User, tenantId?: number | null): Promise<void> {
    await this.repository.removeAllUserRoles(user.id, tenantId)
  }
}
