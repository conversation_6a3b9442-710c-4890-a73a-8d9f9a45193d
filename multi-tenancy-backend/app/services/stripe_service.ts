import Stripe from 'stripe'
import env from '#start/env'
import { inject } from '@adonisjs/core'
import { Logger } from '@adonisjs/core/logger'

@inject()
export default class StripeService {
  private stripe: Stripe

  constructor(protected logger: Logger) {
    this.stripe = new Stripe(env.get('STRIPE_SECRET_KEY'), {
      apiVersion: '2025-02-24.acacia',
    })
  }

  /**
   * Create a customer in Stripe
   */
  async createCustomer(email: string, name?: string): Promise<Stripe.Customer> {
    return await this.stripe.customers.create({
      email,
      name,
    })
  }

  /**
   * Create a payment intent for subscription
   */
  async createPaymentIntent(
    customerId: string,
    priceId: string,
    tenantId: number
  ): Promise<Stripe.PaymentIntent> {
    return await this.stripe.paymentIntents.create({
      customer: customerId,
      amount: await this.getPriceAmount(priceId),
      currency: 'usd',
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        tenantId: tenantId.toString(),
        priceId,
      },
    })
  }

  /**
   * Create a subscription
   */
  async createSubscription(
    customerId: string,
    priceId: string,
    tenantId: number
  ): Promise<Stripe.Subscription> {
    return await this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        tenantId: tenantId.toString(),
      },
    })
  }

  /**
   * Get price amount from Stripe
   */
  async getPriceAmount(priceId: string): Promise<number> {
    const price = await this.stripe.prices.retrieve(priceId)
    return price.unit_amount || 0
  }

  /**
   * Cancel a subscription immediately
   */
  async cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return await this.stripe.subscriptions.cancel(subscriptionId)
  }

  /**
   * Get a subscription by ID
   */
  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    return await this.stripe.subscriptions.retrieve(subscriptionId)
  }

  /**
   * List all subscriptions for a customer
   */
  async listCustomerSubscriptions(customerId: string): Promise<Stripe.Subscription[]> {
    const subscriptions = await this.stripe.subscriptions.list({
      customer: customerId,
      status: 'all',
    })
    return subscriptions.data
  }

  /**
   * Create Stripe Checkout session for subscription
   */
  async createCheckoutSession(
    priceId: string,
    customerEmail: string,
    tenantId: number,
    successUrl: string,
    cancelUrl: string,
    existingCustomerId?: string
  ): Promise<Stripe.Checkout.Session> {
    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      mode: 'subscription',
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        tenantId: tenantId.toString(),
        priceId,
      },
      subscription_data: {
        metadata: {
          tenantId: tenantId.toString(),
        },
      },
    }

    // If customer already exists, use their ID, otherwise use email
    if (existingCustomerId) {
      sessionParams.customer = existingCustomerId
    } else {
      sessionParams.customer_email = customerEmail
    }

    return await this.stripe.checkout.sessions.create(sessionParams)
  }

  /**
   * Update existing subscription to new price (for plan upgrades/downgrades)
   */
  async updateSubscription(
    subscriptionId: string,
    newPriceId: string,
    prorationBehavior: 'always_invoice' | 'create_prorations' | 'none' = 'always_invoice'
  ): Promise<Stripe.Subscription> {
    // Get the current subscription
    const subscription = await this.stripe.subscriptions.retrieve(subscriptionId)

    if (!subscription.items.data[0]) {
      throw new Error('Subscription has no items')
    }

    // Update the subscription with the new price
    return await this.stripe.subscriptions.update(subscriptionId, {
      items: [
        {
          id: subscription.items.data[0].id,
          price: newPriceId,
        },
      ],
      proration_behavior: prorationBehavior,
    })
  }

  /**
   * Preview upcoming invoice for subscription change
   */
  async previewSubscriptionChange(
    customerId: string,
    subscriptionId: string,
    newPriceId: string
  ): Promise<Stripe.UpcomingInvoice> {
    const subscription = await this.stripe.subscriptions.retrieve(subscriptionId)

    if (!subscription.items.data[0]) {
      throw new Error('Subscription has no items')
    }

    return await this.stripe.invoices.retrieveUpcoming({
      customer: customerId,
      subscription: subscriptionId,
      subscription_items: [
        {
          id: subscription.items.data[0].id,
          price: newPriceId,
        },
      ],
      subscription_proration_behavior: 'always_invoice',
    })
  }

  /**
   * Create customer portal session
   */
  async createCustomerPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<Stripe.BillingPortal.Session> {
    return await this.stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    })
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload: string, signature: string): Stripe.Event {
    return this.stripe.webhooks.constructEvent(payload, signature, env.get('STRIPE_WEBHOOK_SECRET'))
  }
}
