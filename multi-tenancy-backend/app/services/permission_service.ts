import { inject } from '@adonisjs/core'
import Permission from '#models/permission'
import BaseService from '#services/base_service'
import PermissionRepository from '#repositories/permission_repository'

@inject()
export default class PermissionService extends BaseService<typeof Permission> {
  protected model = Permission

  constructor(protected repository: PermissionRepository) {
    super()
  }

  /**
   * Find permission by ID
   */
  async findById(id: number): Promise<Permission | null> {
    return await this.repository.findById(id)
  }

  /**
   * Find permission by name
   */
  async findByName(name: string): Promise<Permission | null> {
    return await this.repository.findBy('name', name)
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(): Promise<Permission[]> {
    return await this.repository.findWhere({})
  }

  /**
   * Create a new permission
   */
  async createPermission(data: { name: string; description?: string }): Promise<Permission> {
    return await this.repository.create(data)
  }

  /**
   * Update a permission
   */
  async updatePermission(id: number, data: { name?: string; description?: string }): Promise<Permission> {
    return await this.repository.update(id, data)
  }

  /**
   * Delete a permission
   */
  async deletePermission(id: number): Promise<void> {
    await this.repository.delete(id)
  }
}
