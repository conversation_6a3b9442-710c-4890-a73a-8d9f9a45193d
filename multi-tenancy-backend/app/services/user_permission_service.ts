import { inject } from '@adonisjs/core'
import User from '#models/user'
import UserPermission from '#models/user_permission'
import BaseService from '#services/base_service'
import UserPermissionRepository from '#repositories/user_permission_repository'

@inject()
export default class UserPermissionService extends BaseService<typeof UserPermission> {
  protected model = UserPermission

  constructor(protected repository: UserPermissionRepository) {
    super()
  }

  /**
   * Get all direct permissions for a user (optionally filtered by tenant)
   */
  async getUserDirectPermissions(user: User, tenantId?: number | null): Promise<string[]> {
    return await this.repository.getUserDirectPermissions(user.id, tenantId)
  }

  /**
   * Check if user has a specific direct permission (optionally within a tenant)
   */
  async hasDirectPermission(
    user: User,
    permissionName: string,
    tenantId?: number | null
  ): Promise<boolean> {
    return await this.repository.hasDirectPermission(user.id, permissionName, tenantId)
  }

  /**
   * Check if user has any of the specified direct permissions
   */
  async hasAnyDirectPermission(
    user: User,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    return await this.repository.hasAnyDirectPermission(user.id, permissionNames, tenantId)
  }

  /**
   * Check if user has all of the specified direct permissions
   */
  async hasAllDirectPermissions(
    user: User,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    return await this.repository.hasAllDirectPermissions(user.id, permissionNames, tenantId)
  }

  /**
   * Assign a permission directly to the user (optionally within a tenant)
   */
  async assignPermission(
    user: User,
    permissionId: number,
    tenantId?: number | null
  ): Promise<void> {
    await this.repository.assignPermission(user.id, permissionId, tenantId)
  }

  /**
   * Remove a permission from the user (optionally within a tenant)
   */
  async removePermission(
    user: User,
    permissionId: number,
    tenantId?: number | null
  ): Promise<void> {
    await this.repository.removePermission(user.id, permissionId, tenantId)
  }

  /**
   * Assign a permission by name to the user (optionally within a tenant)
   */
  async assignPermissionByName(
    user: User,
    permissionName: string,
    tenantId?: number | null
  ): Promise<void> {
    await this.repository.assignPermissionByName(user.id, permissionName, tenantId)
  }

  /**
   * Remove a permission by name from the user (optionally within a tenant)
   */
  async removePermissionByName(
    user: User,
    permissionName: string,
    tenantId?: number | null
  ): Promise<void> {
    await this.repository.removePermissionByName(user.id, permissionName, tenantId)
  }

  /**
   * Bulk assign permissions to a user
   */
  async bulkAssignPermissions(
    user: User,
    permissionAssignments: Array<{ permissionId: number; tenantId?: number | null }>
  ): Promise<void> {
    await this.repository.bulkAssignPermissions(user.id, permissionAssignments)
  }

  /**
   * Remove all direct permissions from a user (optionally within a specific tenant)
   */
  async removeAllPermissions(user: User, tenantId?: number | null): Promise<void> {
    await this.repository.removeAllUserPermissions(user.id, tenantId)
  }

  /**
   * Get user direct permissions formatted for API responses
   */
  async getUserDirectPermissionsFormatted(user: User, tenantId?: number | null) {
    return await this.repository.getUserDirectPermissionsFormatted(user.id, tenantId)
  }
}
