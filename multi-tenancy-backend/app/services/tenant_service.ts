import { inject } from '@adonisjs/core'
import Tenant, { TenantStatus } from '#models/tenant'
import { DateTime } from 'luxon'
import BaseService from '#services/base_service'
import { BaseFilters, BasePaginationResult } from '../types/service.js'
import UserRoleService from '#services/user_role_service'
import BillingService from '#services/billing_service'
import TenantRepository from '#repositories/tenant_repository'
import Role from '#models/role'
import User from '#models/user'
import { BillingPlan, BillingCycle } from '#models/billing'
import { cuid } from '@adonisjs/core/helpers'

export interface TenantFilters extends BaseFilters {
  status?: TenantStatus
}

export interface CreateTenantData {
  name: string
  slug: string
  status?: TenantStatus
  trialEndsAt?: DateTime | Date | null
}

export interface UpdateTenantData {
  name?: string
  slug?: string
  status?: TenantStatus
  trialEndsAt?: DateTime | Date | null
}

@inject()
export default class TenantService extends BaseService<typeof Tenant> {
  protected model = Tenant
  protected repository: TenantRepository

  constructor(
    protected userRoleService: UserRoleService,
    tenantRepository: TenantRepository,
    protected billingService: BillingService
  ) {
    super()
    this.repository = tenantRepository
  }

  private get tenantRepository() {
    return this.repository
  }

  /**
   * Get all tenants with filtering and pagination
   */
  async getTenants(filters: TenantFilters = {}): Promise<BasePaginationResult<Tenant>> {
    return await this.tenantRepository.getTenants(filters)
  }

  /**
   * Get a single tenant by ID
   */
  async getTenantById(id: number): Promise<Tenant | null> {
    return await this.tenantRepository.findById(id)
  }

  /**
   * Get a tenant by slug
   */
  async getTenantBySlug(slug: string): Promise<Tenant | null> {
    return await this.tenantRepository.findBySlug(slug)
  }

  /**
   * Create a new tenant (standard CRUD method)
   */
  async create(data: Partial<Tenant>): Promise<Tenant> {
    return await this.tenantRepository.create(data)
  }

  /**
   * Update a tenant (standard CRUD method)
   */
  async update(id: number, data: Partial<Tenant>): Promise<Tenant> {
    return await this.tenantRepository.update(id, data)
  }

  /**
   * Delete a tenant (standard CRUD method)
   */
  async delete(id: number): Promise<void> {
    return await this.tenantRepository.delete(id)
  }

  /**
   * Find tenant by ID or throw error (standard CRUD method)
   */
  async findOrFail(id: number): Promise<Tenant> {
    return await this.tenantRepository.findByIdOrFail(id)
  }

  /**
   * Create a new tenant with auto-generated slug and assign TENANT_OWNER role to creator
   */
  async createTenantSimple(name: string, creatorUser: User): Promise<Tenant> {
    // Generate unique slug
    const slug = cuid()

    // Create tenant using repository
    const tenant = await this.tenantRepository.createTenant({
      name,
      slug,
      status: TenantStatus.TRIAL,
      trialEndsAt: null,
    })

    // Find the TENANT_OWNER role
    const tenantOwnerRole = await Role.findByOrFail('name', 'tenant_owner')

    // Assign TENANT_OWNER role to the creator
    await this.userRoleService.assignRole(creatorUser, tenantOwnerRole.id, tenant.id)

    return tenant
  }

  /**
   * Create default billing record for a new tenant
   */
  public async createDefaultBilling(tenantId: number, user: User): Promise<void> {
    // Create billing record with FREE plan and 14-day trial
    await this.billingService.createBilling(
      {
        tenantId,
        plan: BillingPlan.FREE,
        cycle: BillingCycle.MONTHLY,
        amount: 0, // Free plan
        currency: 'usd',
        trialDays: 14, // 14-day trial period
      },
      user
    )
  }

  /**
   * Update an existing tenant
   */
  async updateTenant(id: number, data: UpdateTenantData): Promise<Tenant> {
    const tenant = await this.getTenantById(id)
    if (!tenant) {
      throw new Error('Tenant not found')
    }

    // Check if slug is being updated and if it already exists
    if (data.slug && data.slug !== tenant.slug) {
      const existingTenant = await this.getTenantBySlug(data.slug)
      if (existingTenant) {
        throw new Error('Tenant with this slug already exists')
      }
    }

    // Filter out undefined values and convert DateTime to Date
    const updateData: UpdateTenantData = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.slug !== undefined) updateData.slug = data.slug
    if (data.status !== undefined) updateData.status = data.status
    if (data.trialEndsAt !== undefined) {
      updateData.trialEndsAt = data.trialEndsAt
        ? data.trialEndsAt instanceof Date
          ? data.trialEndsAt
          : data.trialEndsAt.toJSDate()
        : null
    }

    return await this.tenantRepository.update(id, updateData)
  }

  /**
   * Update tenant status
   */
  async updateTenantStatus(id: number, status: TenantStatus): Promise<Tenant> {
    return await this.tenantRepository.update(id, { status })
  }

  /**
   * Delete a tenant
   */
  async deleteTenant(id: number): Promise<void> {
    const tenant = await this.getTenantById(id)
    if (!tenant) {
      throw new Error('Tenant not found')
    }

    // Load users to check if tenant has users
    await tenant.load('users')
    if (tenant.users.length > 0) {
      throw new Error('Cannot delete tenant with existing users')
    }

    await this.tenantRepository.delete(id)
  }

  /**
   * Get tenant analytics/statistics
   */
  async getTenantAnalytics(id: number) {
    const tenant = await this.getTenantById(id)
    if (!tenant) {
      throw new Error('Tenant not found')
    }

    await tenant.load('users')

    return {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      status: tenant.status,
      userCount: tenant.users.length,
      isOnTrial: tenant.isOnTrial,
      trialExpired: tenant.trialExpired,
      trialEndsAt: tenant.trialEndsAt,
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,
    }
  }
}
