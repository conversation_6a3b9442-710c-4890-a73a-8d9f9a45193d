import { inject } from '@adonisjs/core'
import Stripe from 'stripe'
import BillingService from '#services/billing_service'
import PricingPlanService from '#services/pricing_plan_service'
import StripeService from '#services/stripe_service'
import { BillingStatus } from '#models/billing'
import { Logger } from '@adonisjs/core/logger'
import { DateTime } from 'luxon'

@inject()
export default class StripeWebhookService {
  constructor(
    private billingService: BillingService,
    private pricingPlanService: PricingPlanService,
    private stripeService: StripeService,
    protected logger: Logger
  ) {}

  /**
   * Handle successful subscription webhook
   */
  async handleSubscriptionSuccess(subscription: Stripe.Subscription): Promise<void> {
    const tenantId = parseInt(subscription.metadata.tenantId)

    if (!tenantId) {
      throw new Error('Missing tenant ID in subscription metadata')
    }

    const billing = await this.billingService.getByTenantId(tenantId)

    if (!billing) {
      throw new Error(`No billing record found for tenant ${tenantId}`)
    }
    // Get the current period from subscription items
    const subscriptionItem = subscription.items.data[0]
    const currentPeriodStart =
      subscriptionItem?.current_period_start || subscription.current_period_start
    const currentPeriodEnd = subscriptionItem?.current_period_end || subscription.current_period_end
    // Update billing with subscription details
    const planDetails = await this.pricingPlanService.getPlanByStripePriceId(
      subscription.items.data[0].price.id
    )
    await this.billingService.updateBilling(billing.id, {
      plan: planDetails?.plan.slug,
      cycle: planDetails?.cycle,
      status: BillingStatus.ACTIVE,
      stripeSubscriptionId: subscription.id,
      stripeCustomerId: subscription.customer as string,
      currentPeriodStart: DateTime.fromSeconds(currentPeriodStart),
      currentPeriodEnd: DateTime.fromSeconds(currentPeriodEnd),
      stripePriceId: subscription.items.data[0].price.id,
    })
  }

  /**
   * Handle failed payment webhook
   */
  async handlePaymentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const tenantId = parseInt(paymentIntent.metadata.tenantId)

    if (!tenantId) {
      return // No tenant to update
    }

    const billing = await this.billingService.getByTenantId(tenantId)
    if (billing) {
      await this.billingService.updateBilling(billing.id, {
        status: BillingStatus.SUSPENDED,
      })
    }
  }

  /**
   * Handle subscription update webhook
   */
  async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const tenantIdString = subscription.metadata.tenantId
    const tenantId = parseInt(tenantIdString)

    this.logger.info(
      {
        tenantIdString,
        tenantId,
        subscriptionId: subscription.id,
        customerId: subscription.customer,
        status: subscription.status,
        metadata: subscription.metadata,
      },
      'Handling subscription updated'
    )

    if (!tenantId || isNaN(tenantId)) {
      this.logger.warn(
        {
          tenantIdString,
          tenantId,
          subscriptionId: subscription.id,
          metadata: subscription.metadata,
        },
        'Invalid or missing tenant ID in subscription metadata'
      )
      return
    }

    try {
      const billing = await this.billingService.getByTenantId(tenantId)
      if (!billing) {
        this.logger.warn(
          {
            tenantId,
            subscriptionId: subscription.id,
          },
          'No billing record found for tenant'
        )
        return
      }

      if (!subscription.items.data[0]) {
        this.logger.warn(
          {
            tenantId,
            subscriptionId: subscription.id,
          },
          'No subscription items found'
        )
        return
      }

      const priceId = subscription.items.data[0].price.id
      this.logger.info(
        {
          tenantId,
          subscriptionId: subscription.id,
          priceId,
        },
        'Getting plan details for price ID'
      )

      // Get plan details from the new price ID
      const planDetails = await this.pricingPlanService.getPlanByStripePriceId(priceId)
      if (!planDetails) {
        this.logger.warn(
          {
            tenantId,
            subscriptionId: subscription.id,
            priceId,
          },
          'No plan details found for price ID'
        )
        return
      }

      this.logger.info(
        {
          tenantId,
          billingId: billing.id,
          subscriptionId: subscription.id,
          previousPlan: billing.plan,
          newPlan: planDetails.plan.slug,
          previousCycle: billing.cycle,
          newCycle: planDetails.cycle,
        },
        'Updating billing with new subscription details'
      )

      // Get the current period from subscription items
      const subscriptionItem = subscription.items.data[0]
      const currentPeriodStart =
        subscriptionItem?.current_period_start || subscription.current_period_start
      const currentPeriodEnd =
        subscriptionItem?.current_period_end || subscription.current_period_end

      await this.billingService.updateBilling(billing.id, {
        plan: planDetails.plan.slug,
        cycle: planDetails.cycle,
        status: BillingStatus.ACTIVE,
        stripeSubscriptionId: subscription.id,
        currentPeriodStart: DateTime.fromSeconds(currentPeriodStart),
        currentPeriodEnd: DateTime.fromSeconds(currentPeriodEnd),
        stripePriceId: priceId,
      })

      this.logger.info(
        {
          tenantId,
          billingId: billing.id,
          subscriptionId: subscription.id,
          plan: planDetails.plan.slug,
          cycle: planDetails.cycle,
        },
        'Successfully updated billing with new subscription details'
      )
    } catch (error) {
      this.logger.error(
        {
          tenantId,
          subscriptionId: subscription.id,
          error: error.message,
          stack: error.stack,
        },
        'Failed to handle subscription update'
      )
      throw error
    }
  }

  /**
   * Handle subscription cancellation webhook
   */
  async handleSubscriptionCanceled(subscription: Stripe.Subscription): Promise<void> {
    const tenantIdString = subscription.metadata.tenantId
    const tenantId = parseInt(tenantIdString)

    this.logger.info(
      {
        tenantIdString,
        tenantId,
        subscriptionId: subscription.id,
        customerId: subscription.customer,
        status: subscription.status,
        canceledAt: subscription.canceled_at,
        endedAt: subscription.ended_at,
        metadata: subscription.metadata,
      },
      'Handling subscription canceled'
    )

    if (!tenantId || isNaN(tenantId)) {
      this.logger.warn(
        {
          tenantIdString,
          tenantId,
          subscriptionId: subscription.id,
          metadata: subscription.metadata,
        },
        'Invalid or missing tenant ID in subscription metadata'
      )
      return
    }

    try {
      const billing = await this.billingService.getByTenantId(tenantId)
      if (!billing) {
        this.logger.warn(
          {
            tenantId,
            subscriptionId: subscription.id,
          },
          'No billing record found for tenant'
        )
        return
      }

      this.logger.info(
        {
          tenantId,
          billingId: billing.id,
          subscriptionId: subscription.id,
          previousStatus: billing.status,
        },
        'Updating billing status to cancelled'
      )

      await this.billingService.updateBilling(billing.id, {
        status: BillingStatus.CANCELLED,
        cancelledAt: DateTime.now(),
      })

      this.logger.info(
        {
          tenantId,
          billingId: billing.id,
          subscriptionId: subscription.id,
        },
        'Successfully updated billing status to cancelled'
      )
    } catch (error) {
      this.logger.error(
        {
          tenantId,
          subscriptionId: subscription.id,
          error: error.message,
          stack: error.stack,
        },
        'Failed to handle subscription cancellation'
      )
      throw error
    }
  }

  /**
   * Handle successful invoice payment webhook
   */
  async handleInvoicePaymentSuccess(invoice: Stripe.Invoice): Promise<void> {
    if (!invoice.subscription) {
      return // Not a subscription invoice
    }

    const subscription = await this.stripeService.getSubscription(invoice.subscription as string)
    const tenantId = parseInt(subscription.metadata.tenantId)

    if (!tenantId) {
      return
    }

    const billing = await this.billingService.getByTenantId(tenantId)
    if (billing) {
      await this.billingService.updateBilling(billing.id, {
        status: BillingStatus.ACTIVE,
        amount: invoice.amount_paid,
        currency: invoice.currency,
        currentPeriodStart: DateTime.fromSeconds(subscription.current_period_start),
        currentPeriodEnd: DateTime.fromSeconds(subscription.current_period_end),
      })
    }
  }

  /**
   * Handle failed invoice payment webhook
   */
  async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    if (!invoice.subscription) {
      return // Not a subscription invoice
    }

    const subscription = await this.stripeService.getSubscription(invoice.subscription as string)
    const tenantId = parseInt(subscription.metadata.tenantId)

    if (!tenantId) {
      return
    }

    const billing = await this.billingService.getByTenantId(tenantId)
    if (billing) {
      await this.billingService.updateBilling(billing.id, {
        status: BillingStatus.SUSPENDED,
      })
    }
  }
}
