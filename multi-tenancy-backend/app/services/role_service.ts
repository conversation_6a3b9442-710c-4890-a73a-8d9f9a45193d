import { inject } from '@adonisjs/core'
import Role from '#models/role'
import BaseService from '#services/base_service'
import RoleRepository from '#repositories/role_repository'

@inject()
export default class RoleService extends BaseService<typeof Role> {
  protected model = Role

  constructor(protected repository: RoleRepository) {
    super()
  }

  /**
   * Find role by ID
   */
  async findById(id: number): Promise<Role | null> {
    return await this.repository.findById(id)
  }

  /**
   * Find role by name
   */
  async findByName(name: string): Promise<Role | null> {
    return await this.repository.findBy('name', name)
  }

  /**
   * Get all roles
   */
  async getAllRoles(): Promise<Role[]> {
    return await this.repository.findWhere({})
  }

  /**
   * Create a new role
   */
  async createRole(data: { name: string; description?: string }): Promise<Role> {
    return await this.repository.create(data)
  }

  /**
   * Update a role
   */
  async updateRole(id: number, data: { name?: string; description?: string }): Promise<Role> {
    return await this.repository.update(id, data)
  }

  /**
   * Delete a role
   */
  async deleteRole(id: number): Promise<void> {
    await this.repository.delete(id)
  }
}
