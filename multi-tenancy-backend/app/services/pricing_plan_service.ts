import { inject } from '@adonisjs/core'
import PricingPlan from '#models/pricing_plan'
import { BillingPlan, BillingCycle } from '#models/billing'
import BaseService from '#services/base_service'

export interface PricingPlanFilters {
  isActive?: boolean
  isPopular?: boolean
  search?: string
}

@inject()
export default class PricingPlanService extends BaseService<typeof PricingPlan> {
  protected model = PricingPlan
  protected repository: any // This service doesn't use a repository pattern

  constructor() {
    super()
  }

  /**
   * Get all active pricing plans ordered by sort order
   */
  async getActivePlans(): Promise<PricingPlan[]> {
    return await PricingPlan.query().where('isActive', true).orderBy('sortOrder', 'asc')
  }

  /**
   * Get pricing plan by slug
   */
  async getPlanBySlug(slug: BillingPlan): Promise<PricingPlan | null> {
    return await PricingPlan.query().where('slug', slug).first()
  }

  /**
   * Get pricing plan by slug or fail
   */
  async getPlanBySlugOrFail(slug: BillingPlan): Promise<PricingPlan> {
    const plan = await this.getPlanBySlug(slug)
    if (!plan) {
      throw new Error(`Pricing plan with slug '${slug}' not found`)
    }
    return plan
  }

  /**
   * Get popular plans
   */
  async getPopularPlans(): Promise<PricingPlan[]> {
    return await PricingPlan.query()
      .where('isActive', true)
      .where('isPopular', true)
      .orderBy('sortOrder', 'asc')
  }

  /**
   * Get plan price for specific cycle
   */
  async getPlanPrice(slug: BillingPlan, cycle: BillingCycle): Promise<number> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.getPrice(cycle)
  }

  /**
   * Get plan features
   */
  async getPlanFeatures(slug: BillingPlan): Promise<any[]> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.featuresArray
  }

  /**
   * Get plan limits
   */
  async getPlanLimits(slug: BillingPlan): Promise<any> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.limitsObject
  }

  /**
   * Check if plan has specific feature
   */
  async planHasFeature(slug: BillingPlan, featureName: string): Promise<boolean> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.hasFeature(featureName)
  }

  /**
   * Get feature limit for a plan
   */
  async getPlanFeatureLimit(slug: BillingPlan, featureName: string): Promise<number | null> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.getFeatureLimit(featureName)
  }

  /**
   * Get Stripe price ID for plan and cycle
   */
  async getStripePriceId(slug: BillingPlan, cycle: BillingCycle): Promise<string | null> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.getStripePriceId(cycle)
  }

  /**
   * Get pricing plan by Stripe price ID
   */
  async getPlanByStripePriceId(
    stripePriceId: string
  ): Promise<{ plan: PricingPlan; cycle: BillingCycle; pricingPlan: PricingPlan } | null> {
    // Check monthly price IDs
    const monthlyPlan = await PricingPlan.query()
      .where('stripePriceIdMonthly', stripePriceId)
      .where('isActive', true)
      .first()

    if (monthlyPlan) {
      return { plan: monthlyPlan, cycle: BillingCycle.MONTHLY, pricingPlan: monthlyPlan }
    }

    // Check yearly price IDs
    const yearlyPlan = await PricingPlan.query()
      .where('stripePriceIdYearly', stripePriceId)
      .where('isActive', true)
      .first()

    if (yearlyPlan) {
      return { plan: yearlyPlan, cycle: BillingCycle.YEARLY, pricingPlan: yearlyPlan }
    }

    return null
  }

  /**
   * Calculate yearly savings percentage
   */
  async getYearlySavings(slug: BillingPlan): Promise<number> {
    const plan = await this.getPlanBySlugOrFail(slug)
    return plan.yearlySavingsPercentage
  }

  /**
   * Get plans comparison data
   */
  async getPlansComparison(): Promise<any[]> {
    const plans = await this.getActivePlans()

    return plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      slug: plan.slug,
      description: plan.description,
      monthlyPrice: plan.monthlyPrice,
      yearlyPrice: plan.yearlyPrice,
      currency: plan.currency,
      isPopular: plan.isPopular,
      features: plan.featuresArray,
      limits: plan.limitsObject,
      trialDays: plan.trialDays,
      yearlySavings: plan.yearlySavingsPercentage,
      stripePriceIdMonthly: plan.stripePriceIdMonthly,
      stripePriceIdYearly: plan.stripePriceIdYearly,
    }))
  }

  /**
   * Get plan upgrade path (plans that are higher tier than current plan)
   */
  async getUpgradePlans(currentSlug: BillingPlan): Promise<PricingPlan[]> {
    const currentPlan = await this.getPlanBySlugOrFail(currentSlug)

    return await PricingPlan.query()
      .where('isActive', true)
      .where('sortOrder', '>', currentPlan.sortOrder)
      .orderBy('sortOrder', 'asc')
  }

  /**
   * Get plan downgrade path (plans that are lower tier than current plan)
   */
  async getDowngradePlans(currentSlug: BillingPlan): Promise<PricingPlan[]> {
    const currentPlan = await this.getPlanBySlugOrFail(currentSlug)

    return await PricingPlan.query()
      .where('isActive', true)
      .where('sortOrder', '<', currentPlan.sortOrder)
      .orderBy('sortOrder', 'desc')
  }

  /**
   * Check if plan upgrade is available
   */
  async canUpgrade(currentSlug: BillingPlan): Promise<boolean> {
    const upgradePlans = await this.getUpgradePlans(currentSlug)
    return upgradePlans.length > 0
  }

  /**
   * Check if plan downgrade is available
   */
  async canDowngrade(currentSlug: BillingPlan): Promise<boolean> {
    const downgradePlans = await this.getDowngradePlans(currentSlug)
    return downgradePlans.length > 0
  }

  /**
   * Get next higher tier plan
   */
  async getNextUpgradePlan(currentSlug: BillingPlan): Promise<PricingPlan | null> {
    const upgradePlans = await this.getUpgradePlans(currentSlug)
    return upgradePlans.length > 0 ? upgradePlans[0] : null
  }

  /**
   * Get next lower tier plan
   */
  async getNextDowngradePlan(currentSlug: BillingPlan): Promise<PricingPlan | null> {
    const downgradePlans = await this.getDowngradePlans(currentSlug)
    return downgradePlans.length > 0 ? downgradePlans[0] : null
  }
}
