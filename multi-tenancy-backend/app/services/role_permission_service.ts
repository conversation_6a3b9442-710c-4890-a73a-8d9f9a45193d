import { inject } from '@adonisjs/core'
import RolePermission from '#models/role_permission'
import BaseService from '#services/base_service'
import RoleRepository from '#repositories/role_repository'

import { Role as RoleEnum } from '#enums/role'
import Role from '#models/role'
import Permission from '#models/permission'
import { BaseFilters, BasePaginationResult } from '../types/service.js'

export interface RoleFilters extends BaseFilters {
  resource?: string
}

export interface CreateRoleData {
  name: string
  displayName: string
  description?: string
  permissionIds?: number[]
}

export interface UpdateRoleData {
  name?: string
  displayName?: string
  description?: string
  permissionIds?: number[]
}

@inject()
export default class RolePermissionService extends BaseService<typeof Role> {
  protected model = Role
  protected repository: RoleRepository

  constructor(roleRepository: RoleRepository) {
    super()
    this.repository = roleRepository
  }

  private get roleRepository() {
    return this.repository
  }

  /**
   * Get paginated roles with their permissions (excluding super_admin)
   */
  async getPaginatedRoles(filters: RoleFilters = {}): Promise<BasePaginationResult<Role>> {
    return await this.roleRepository.getPaginated(filters)
  }

  /**
   * Find role by ID
   */
  async findById(id: number): Promise<Role | null> {
    return await this.roleRepository.findById(id)
  }

  /**
   * Get all permissions for a role (optionally filtered by tenant)
   */
  async getRolePermissions(roleName: RoleEnum): Promise<string[]> {
    const role = await Role.findBy('name', roleName)
    if (!role) {
      return []
    }

    const rolePermissions = await RolePermission.query()
      .where('roleId', role.id)
      .preload('permission')
    return rolePermissions.map((rolePermission) => rolePermission.permission.name)
  }

  /**
   * Get paginated permissions with filtering
   */
  async getPaginatedPermissions(
    filters: RoleFilters = {}
  ): Promise<BasePaginationResult<Permission>> {
    const { page = 1, limit = 50, resource } = filters

    const query = Permission.query()

    if (resource) {
      query.where('resource', resource)
    }

    const permissions = await query.paginate(page, limit)

    return {
      data: permissions.all(),
      pagination: {
        currentPage: permissions.currentPage,
        perPage: permissions.perPage,
        total: permissions.total,
        lastPage: permissions.lastPage,
        hasMorePages: permissions.hasMorePages,
        hasPages: permissions.hasPages,
      },
    }
  }

  /**
   * Create a new role
   */
  async createRole(data: CreateRoleData): Promise<Role> {
    // Check if role name already exists
    const existingRole = await Role.findBy('name', data.name)
    if (existingRole) {
      throw new Error('Role with this name already exists')
    }

    const role = await Role.create({
      name: data.name,
      displayName: data.displayName,
      description: data.description,
      isSystem: false, // User-created roles are not system roles
    })

    // Assign permissions if provided
    if (data.permissionIds && Array.isArray(data.permissionIds)) {
      await role.related('permissions').sync(data.permissionIds)
    }

    await role.load('permissions')
    return role
  }

  /**
   * Update a role
   */
  async updateRole(id: number, data: UpdateRoleData): Promise<Role> {
    const role = await Role.findOrFail(id)

    // Check if new name conflicts with existing role
    if (data.name && data.name !== role.name) {
      const existingRole = await Role.findBy('name', data.name)
      if (existingRole) {
        throw new Error('Role with this name already exists')
      }
    }

    role.merge({
      name: data.name || role.name,
      displayName: data.displayName || role.displayName,
      description: data.description || role.description,
    })

    await role.save()

    // Update permissions if provided
    if (data.permissionIds && Array.isArray(data.permissionIds)) {
      await role.related('permissions').sync(data.permissionIds)
    }

    await role.load('permissions')
    return role
  }

  /**
   * Delete a role
   */
  async deleteRole(id: number): Promise<void> {
    const role = await Role.findOrFail(id)

    // Prevent deleting system roles
    if (role.isSystem) {
      throw new Error('System roles cannot be deleted')
    }

    await role.delete()
  }

  /**
   * Assign permissions to a role
   */
  async assignPermissions(roleId: number, permissionIds: number[]): Promise<Role> {
    const role = await Role.findOrFail(roleId)

    if (!Array.isArray(permissionIds)) {
      throw new Error('Permission IDs must be an array')
    }

    await role.related('permissions').sync(permissionIds)
    await role.load('permissions')

    return role
  }
}
