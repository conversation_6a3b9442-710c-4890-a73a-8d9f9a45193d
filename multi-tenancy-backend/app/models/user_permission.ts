import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Permission from '#models/permission'
import Tenant from '#models/tenant'

export default class UserPermission extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare permissionId: number

  @column()
  declare tenantId: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Permission)
  declare permission: BelongsTo<typeof Permission>

  @belongsTo(() => Tenant)
  declare tenant: BelongsTo<typeof Tenant>

  // Helper methods
  get isGlobalPermission() {
    return this.tenantId === null
  }

  get isTenantSpecific() {
    return this.tenantId !== null
  }
}
