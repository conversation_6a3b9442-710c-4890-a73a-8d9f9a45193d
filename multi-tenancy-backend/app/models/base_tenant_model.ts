import TenantContext from '#services/tenant_context'
import { BaseModel, beforeFetch, beforeFind, beforeSave, column } from '@adonisjs/lucid/orm'
import type { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import Tenant from './tenant.js'

export default class BaseTenantModel extends BaseModel {
  @column()
  declare tenantId: number

  @beforeSave()
  static async setTenantId(model: BaseTenantModel) {
    model.tenantId = TenantContext.getTenantId()
  }

  @beforeFind()
  static findTenantId(query: ModelQueryBuilderContract<typeof Tenant>) {
    query.where('tenantId', TenantContext.getTenantId())
  }

  @beforeFetch()
  static getTenantId(query: ModelQueryBuilderContract<typeof Tenant>) {
    query.where('tenantId', TenantContext.getTenantId())
  }
}
