import { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, hasMany, hasManyThrough, manyToMany, scope } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import type { HasMany, HasManyThrough, ManyToMany } from '@adonisjs/lucid/types/relations'
import UserRoleModel from '#models/user_role'
import UserPermission from '#models/user_permission'
import Tenant from '#models/tenant'
import { Database } from '@adonisjs/lucid/database'
import { normalizeEmail } from '#utils/email_helper'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  TENANT_OWNER = 'tenant_owner',
  TENANT_ADMIN = 'tenant_admin',
  MANAGER = 'manager',
  STAFF = 'staff',
  VIEWER = 'viewer',
  CUSTOMER = 'customer',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare firstName: string

  @column()
  declare lastName: string

  @column()
  declare fullName: string | null

  @column()
  declare email: string

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare phone: string | null

  @column()
  declare status: UserStatus

  @column.dateTime()
  declare emailVerifiedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  // Relationships
  @hasMany(() => UserRoleModel)
  declare userRoles: HasMany<typeof UserRoleModel>

  @hasMany(() => UserPermission)
  declare userPermissions: HasMany<typeof UserPermission>

  @manyToMany(() => Tenant, {
    pivotTable: 'user_roles',
    pivotForeignKey: 'user_id',
    pivotRelatedForeignKey: 'tenant_id',
    pivotColumns: ['role_id'],
  })
  declare tenants: ManyToMany<typeof Tenant>

  static accessTokens = DbAccessTokensProvider.forModel(User)

  // Helper methods
  get displayName() {
    return this.fullName || `${this.firstName} ${this.lastName}`
  }

  /**
   * Find user by normalized email for authentication and duplicate checking
   */
  static async findByNormalizedEmail(
    email: string,
    options?: { client?: any }
  ): Promise<User | null> {
    const normalizedEmail = normalizeEmail(email)

    // Get all users and check normalized emails
    const query = options?.client ? this.query({ client: options.client }) : this.query()
    const users = await query.exec()

    for (const user of users) {
      if (normalizeEmail(user.email) === normalizedEmail) {
        return user
      }
    }

    return null
  }

  /**
   * Custom method for verifying credentials with normalized email comparison
   */
  static async verifyCredentialsNormalized(email: string, password: string): Promise<User | null> {
    const user = await this.findByNormalizedEmail(email)
    if (!user) {
      return null
    }
    const isValidPassword = await hash.verify(user.password, password)
    return isValidPassword ? user : null
  }
}
