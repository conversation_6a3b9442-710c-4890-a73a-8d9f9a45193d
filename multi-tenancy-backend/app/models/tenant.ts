import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, hasOne, manyToMany } from '@adonisjs/lucid/orm'
import type { HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import User from '#models/user'
import Billing from './billing.js'
import { PlanLimitationType } from '#enums/plan_limitation'

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
}

export default class Tenant extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare slug: string

  @column()
  declare status: TenantStatus

  @column.dateTime()
  declare trialEndsAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @manyToMany(() => User, {
    pivotTable: 'user_roles',
    pivotForeignKey: 'tenant_id',
    pivotRelatedForeignKey: 'user_id',
    pivotColumns: ['role_id'],
  })
  declare users: ManyToMany<typeof User>

  @hasOne(() => Billing)
  declare billing: HasOne<typeof Billing>

  // Helper methods
  get isActive() {
    return this.status === TenantStatus.ACTIVE
  }

  get isOnTrial() {
    return this.status === TenantStatus.TRIAL
  }

  get trialExpired() {
    return this.trialEndsAt && this.trialEndsAt < DateTime.now()
  }

  // Plan limitation helper methods
  /**
   * Get user count for this tenant
   */
  async getUserCount(): Promise<number> {
    const users = await this.related('users').query()
    return users.length
  }

  /**
   * Check if tenant can perform an action based on plan limitations
   */
  async canPerformAction(
    limitationType: PlanLimitationType,
    currentUsage?: number,
    additionalUsage: number = 1
  ): Promise<boolean> {
    await this.load('billing')

    if (!this.billing) {
      return false // No billing info, deny action
    }

    // If current usage is not provided, we'll need to calculate it based on limitation type
    if (currentUsage === undefined) {
      currentUsage = await this.getCurrentUsage(limitationType)
    }

    return !this.billing.wouldExceedLimit(limitationType, currentUsage, additionalUsage)
  }

  /**
   * Get current usage for a specific limitation type
   */
  async getCurrentUsage(limitationType: PlanLimitationType): Promise<number> {
    switch (limitationType) {
      case PlanLimitationType.USER_NUMBER:
        return await this.getUserCount()
      case PlanLimitationType.STORAGE:
        // TODO: Implement storage usage calculation
        return 0
      case PlanLimitationType.CUSTOMERS:
        // TODO: Implement customer count calculation
        return 0
      case PlanLimitationType.LOCATIONS:
        // TODO: Implement location count calculation
        return 0
      case PlanLimitationType.CAMPAIGNS:
        // TODO: Implement campaign count calculation
        return 0
      case PlanLimitationType.REWARDS:
        // TODO: Implement reward count calculation
        return 0
      case PlanLimitationType.API_CALLS:
        // TODO: Implement API calls calculation
        return 0
      default:
        return 0
    }
  }

  /**
   * Get plan limitation status for all limitation types
   */
  async getPlanLimitationStatus(): Promise<
    Record<
      PlanLimitationType,
      { currentUsage: number; limit: number | null; remaining: number | null; isUnlimited: boolean }
    >
  > {
    await this.load('billing')

    if (!this.billing) {
      throw new Error('Billing information not found for tenant')
    }

    const result: Record<string, any> = {}

    for (const limitationType of Object.values(PlanLimitationType)) {
      const currentUsage = await this.getCurrentUsage(limitationType)
      const limit = this.billing.getPlanLimit(limitationType)
      const isUnlimited = this.billing.hasUnlimitedAccess(limitationType)
      const remaining = this.billing.getRemainingCapacity(limitationType, currentUsage)

      result[limitationType] = {
        currentUsage,
        limit,
        remaining,
        isUnlimited,
      }
    }

    return result as Record<
      PlanLimitationType,
      { currentUsage: number; limit: number | null; remaining: number | null; isUnlimited: boolean }
    >
  }

  /**
   * Check if tenant is approaching any plan limits (>= 80% usage)
   */
  async isApproachingAnyLimit(): Promise<boolean> {
    await this.load('billing')

    if (!this.billing) {
      return false
    }

    for (const limitationType of Object.values(PlanLimitationType)) {
      const currentUsage = await this.getCurrentUsage(limitationType)
      if (this.billing.isApproachingLimit(limitationType, currentUsage)) {
        return true
      }
    }

    return false
  }

  /**
   * Check if tenant has exceeded any plan limits
   */
  async hasExceededAnyLimit(): Promise<boolean> {
    await this.load('billing')

    if (!this.billing) {
      return false
    }

    for (const limitationType of Object.values(PlanLimitationType)) {
      const currentUsage = await this.getCurrentUsage(limitationType)
      if (this.billing.hasExceededLimit(limitationType, currentUsage)) {
        return true
      }
    }

    return false
  }
}
