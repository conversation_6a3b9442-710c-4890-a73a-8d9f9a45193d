import { DateTime } from 'luxon'
import { BaseModel, column, manyToMany } from '@adonisjs/lucid/orm'
import type { ManyToMany } from '@adonisjs/lucid/types/relations'
import Role from '#models/role'

export default class Permission extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare description: string | null

  @column()
  declare resource: string

  @column()
  declare action: string

  @column()
  declare isSystem: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @manyToMany(() => Role, {
    pivotTable: 'role_permissions',
    pivotTimestamps: {
      createdAt: 'created_at',
      updatedAt: false,
    },
  })
  declare roles: ManyToMany<typeof Role>

  // Helper methods
  get isSystemPermission() {
    return this.isSystem
  }

  /**
   * Get the full permission name (resource.action)
   */
  get fullName() {
    return `${this.resource}.${this.action}`
  }

  /**
   * Check if this permission matches a given pattern
   */
  matches(pattern: string): boolean {
    // Exact match
    if (this.name === pattern) {
      return true
    }

    // Wildcard match (e.g., 'merchants.*' matches 'merchants.create')
    if (pattern.endsWith('.*')) {
      const resourcePattern = pattern.slice(0, -2)
      return this.resource === resourcePattern
    }

    // Resource.action match
    if (pattern === this.fullName) {
      return true
    }

    return false
  }

  /**
   * Create permission name from resource and action
   */
  static createName(resource: string, action: string): string {
    return `${resource}.${action}`
  }
}
