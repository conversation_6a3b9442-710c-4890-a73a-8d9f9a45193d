import { DateTime } from 'luxon'
import { BaseModel, column, manyToMany, hasMany } from '@adonisjs/lucid/orm'
import type { ManyToMany, HasMany } from '@adonisjs/lucid/types/relations'
import Permission from '#models/permission'
import UserRole from '#models/user_role'

export default class Role extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare name: string

  @column()
  declare displayName: string

  @column()
  declare description: string | null

  @column()
  declare isSystem: boolean

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @manyToMany(() => Permission, {
    pivotTable: 'role_permissions',
    pivotTimestamps: {
      createdAt: 'created_at',
      updatedAt: false,
    },
  })
  declare permissions: ManyToMany<typeof Permission>

  @hasMany(() => UserRole)
  declare userRoles: <PERSON><PERSON><PERSON><typeof UserRole>

  // Helper methods
  get isSystemRole() {
    return this.isSystem
  }
}
