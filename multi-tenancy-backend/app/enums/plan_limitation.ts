export enum PlanLimitationType {
  USER_NUMBER = 'user_number',
  STORAGE = 'storage',
  CUSTOMERS = 'customers',
  LOCATIONS = 'locations',
  CAMPAIGNS = 'campaigns',
  REWARDS = 'rewards',
  API_CALLS = 'api_calls',
}

export enum PlanLimitationAction {
  CREATE_USER = 'create_user',
  UPLOAD_FILE = 'upload_file',
  CREATE_CUSTOMER = 'create_customer',
  CREATE_LOCATION = 'create_location',
  CREATE_CAMPAIGN = 'create_campaign',
  CREATE_REWARD = 'create_reward',
  API_REQUEST = 'api_request',
}
