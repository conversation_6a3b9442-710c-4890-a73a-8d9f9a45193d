import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import { DateTime } from 'luxon'
import User, { UserRole, UserStatus } from '#models/user'
import Tenant from '#models/tenant'
import UserRoleService from '#services/user_role_service'
import {
  registerValidator,
  loginValidator,
  updateProfileValidator,
  changePasswordValidator,
} from '#validators/auth'
import { HEADERS } from '#constants/headers'

@inject()
export default class AuthController {
  constructor(protected userRoleService: UserRoleService) {}
  /**
   * Assign default role to user based on their type and tenant
   */
  private async assignDefaultRole(user: any, requestedRole?: string, tenantId?: number | null) {
    const { default: Role } = await import('#models/role')

    let defaultRoleName = 'viewer' // Default fallback

    // Determine the appropriate default role
    if (requestedRole === UserRole.SUPER_ADMIN) {
      defaultRoleName = 'super_admin'
      tenantId = null // Super admin is global
    } else if (requestedRole === UserRole.TENANT_OWNER && tenantId) {
      defaultRoleName = 'tenant_owner'
    } else if (requestedRole === UserRole.TENANT_ADMIN && tenantId) {
      defaultRoleName = 'tenant_admin'
    } else if (requestedRole === UserRole.MANAGER && tenantId) {
      defaultRoleName = 'merchant_manager'
    } else if (requestedRole === UserRole.STAFF && tenantId) {
      defaultRoleName = 'merchant_staff'
    } else {
      defaultRoleName = 'viewer'
    }

    // Find the role
    const role = await Role.findBy('name', defaultRoleName)
    if (role) {
      await this.userRoleService.assignRole(user, role.id, tenantId)
    }
  }

  /**
   * Register a new user
   */
  async register({ request, response }: HttpContext) {
    const payload = await request.validateUsing(registerValidator)

    // For super admin, no tenant is required
    let tenantId = null
    if (payload.role !== UserRole.SUPER_ADMIN) {
      // For non-super admin users, we need a tenant
      // This could be determined by subdomain or provided in request
      if (payload.tenantId) {
        const tenant = await Tenant.find(payload.tenantId)
        if (!tenant) {
          return response.badRequest({
            success: false,
            message: 'Invalid tenant',
            error: 'Tenant not found',
          })
        }
        tenantId = tenant.id
      }
    }

    // Check if user already exists using normalized email comparison
    const existingUser = await User.findByNormalizedEmail(payload.email)
    if (existingUser) {
      return response.badRequest({
        success: false,
        message: 'Registration failed',
        error: 'User with this email already exists',
      })
    }

    // Create user
    const user = await User.create({
      firstName: payload.firstName,
      lastName: payload.lastName,
      fullName: `${payload.firstName} ${payload.lastName}`,
      email: payload.email,
      password: payload.password,
      phone: payload.phone,
      status: UserStatus.ACTIVE,
    })

    // Assign default role based on user type
    await this.assignDefaultRole(user, payload.role, tenantId)

    // Generate access token
    const token = await User.accessTokens.create(user, ['*'], {
      expiresIn: '24 hours',
    })

    // Get user roles and permissions
    const rbacData = await this.userRoleService.getUserRolesAndPermissions(user, tenantId)

    return response.created({
      success: true,
      message: 'Registration successful',
      data: {
        accessToken: token.value!.release(),
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          status: user.status,
          ...rbacData,
        },
        expiresIn: '24h',
      },
    })
  }

  /**
   * Login user
   */
  async login({ request, response }: HttpContext) {
    const payload = await request.validateUsing(loginValidator)

    // Verify user credentials using normalized email comparison
    const user = await User.verifyCredentialsNormalized(payload.email, payload.password)

    if (!user) {
      return response.unauthorized({
        success: false,
        message: 'Login failed',
        error: 'Invalid credentials',
      })
    }

    // Check if user is active
    if (user.status !== UserStatus.ACTIVE) {
      return response.unauthorized({
        success: false,
        message: 'Login failed',
        error: 'Account is not active',
      })
    }

    // Generate access token
    const token = await User.accessTokens.create(user, ['*'], {
      expiresIn: '24 hours',
    })

    // Get user roles and permissions (no specific tenant context for login)
    const rbacData = await this.userRoleService.getUserRolesAndPermissions(user)

    return response.ok({
      success: true,
      message: 'Login successful',
      data: {
        accessToken: token.value!.release(),
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          status: user.status,
          ...rbacData,
        },
        expiresIn: '24h',
      },
    })
  }

  /**
   * Get user profile
   */
  async profile({ auth, response, request }: HttpContext) {
    const user = auth.user!
    await user.load('tenants')

    return response.ok({
      success: true,
      message: 'Profile retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          phone: user.phone,
          status: user.status,
          tenants: user.tenants.map((tenant) => ({
            ...tenant.toJSON(),
            roleId: tenant.$extras.pivot_role_id,
            roleName: tenant.$extras.pivot_role_name,
          })),
          emailVerifiedAt: user.emailVerifiedAt,
          createdAt: user.createdAt,
        },
      },
    })
  }

  /**
   * Update user profile
   */
  async updateProfile({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const payload = await request.validateUsing(updateProfileValidator)

    user.merge({
      firstName: payload.firstName || user.firstName,
      lastName: payload.lastName || user.lastName,
      phone: payload.phone || user.phone,
    })

    // Update full name if first or last name changed
    if (payload.firstName || payload.lastName) {
      user.fullName = `${user.firstName} ${user.lastName}`
    }

    await user.save()

    return response.ok({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          phone: user.phone,
          status: user.status,
        },
      },
    })
  }

  /**
   * Change password
   */
  async changePassword({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const payload = await request.validateUsing(changePasswordValidator)

    // Verify current password using normalized email comparison
    const isValidPassword = await User.verifyCredentialsNormalized(
      user.email,
      payload.currentPassword
    )
    if (!isValidPassword) {
      return response.badRequest({
        success: false,
        message: 'Password change failed',
        error: 'Current password is incorrect',
      })
    }

    // Update password
    user.password = payload.newPassword
    await user.save()

    return response.ok({
      success: true,
      message: 'Password changed successfully',
    })
  }

  /**
   * Logout user
   */
  async logout({ auth, response }: HttpContext) {
    const user = auth.user!
    await User.accessTokens.delete(user, user.currentAccessToken.identifier)

    return response.ok({
      success: true,
      message: 'Logout successful',
    })
  }

  /**
   * Verify token
   */
  async verifyToken({ auth, response }: HttpContext) {
    const user = auth.user!

    return response.ok({
      success: true,
      message: 'Token is valid',
      data: {
        user: {
          id: user.id,
          email: user.email,
          status: user.status,
        },
      },
    })
  }

  /**
   * Get available tenants for super admin context switching
   */
  async getAvailableTenants({ auth, response }: HttpContext) {
    const user = auth.user!

    const isSuperAdmin = await this.userRoleService.hasRole(user, 'super_admin', null)

    // Get all tenants for super admin
    let tenantQuery = Tenant.query().orderBy('name', 'asc')

    if (!isSuperAdmin) {
      // Get user roles and filter tenants
      tenantQuery = tenantQuery.whereHas('users', (userQuery) => {
        userQuery.where('user_id', user.id)
      })
    }
    const tenants = await tenantQuery

    return response.ok({
      success: true,
      message: 'Available tenants retrieved successfully',
      data: {
        tenants: tenants.map((tenant) => ({
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
          status: tenant.status,
        })),
      },
    })
  }

  async getMyPermissions({ auth, request, response }: HttpContext) {
    const user = auth.user!
    const headerTenantId = request.header(HEADERS.TENANT_ID)

    // Extract tenant from request (using the same logic as other endpoints)
    const tenant = await Tenant.find(headerTenantId)
    if (!tenant) {
      return response.badRequest({
        success: false,
        message: 'Tenant context required',
        error: 'No tenant specified in request headers',
      })
    }

    const permissions = await this.userRoleService.getUserPermissions(user, tenant.id)
    return response.ok({
      success: true,
      message: 'My permissions retrieved successfully',
      data: { permissions },
    })
  }

  /**
   * Redirect to Google OAuth
   */
  async googleRedirect({ ally }: HttpContext) {
    return ally.use('google').redirect()
  }

  /**
   * Handle Google OAuth callback
   */
  async googleCallback({ request, ally, response }: HttpContext) {
    const google = ally.use('google')

    // Check for errors and redirect to frontend with error
    if (google.accessDenied()) {
      return response.redirect(
        `http://localhost:4444/_auth/google?error=${encodeURIComponent('You have cancelled the login process')}`
      )
    }

    if (google.stateMisMatch()) {
      return response.redirect(
        `http://localhost:4444/_auth/google?error=${encodeURIComponent('We are unable to verify the request. Please try again')}`
      )
    }

    if (google.hasError()) {
      return response.redirect(
        `http://localhost:4444/_auth/google?error=${encodeURIComponent(google.getError() || 'OAuth error occurred')}`
      )
    }

    try {
      // Get user from Google
      const googleUser = await google.user()

      // Check if user already exists
      let user = await User.findByNormalizedEmail(googleUser.email!)

      if (!user) {
        // Create new user
        const [firstName, ...lastNameParts] = googleUser.name?.split(' ') || ['', '']
        const lastName = lastNameParts.join(' ')

        user = await User.create({
          firstName: firstName || 'User',
          lastName: lastName || '',
          fullName: googleUser.name || `${firstName} ${lastName}`,
          email: googleUser.email!,
          password: Math.random().toString(36), // Random password since they'll use OAuth
          status: UserStatus.ACTIVE,
          emailVerifiedAt: googleUser.emailVerificationState === 'verified' ? DateTime.now() : null,
        })

        // Assign default viewer role
        await this.assignDefaultRole(user, 'viewer', null)
      }

      // Generate JWT token
      const token = await User.accessTokens.create(user)

      // Redirect to frontend with success and token
      return response.redirect(
        `http://localhost:4444/_auth/google?success=true&token=${encodeURIComponent(token.value!.release())}`
      )
    } catch (error) {
      console.error('Google OAuth error:', error)
      return response.redirect(
        `http://localhost:4444/_auth/google?error=${encodeURIComponent('Unable to authenticate with Google')}`
      )
    }
  }
}
