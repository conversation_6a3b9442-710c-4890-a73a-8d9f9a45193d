import type { HttpContext } from '@adonisjs/core/http'
import BaseController from '#controllers/base_controller'
import RolePermissionService from '#services/role_permission_service'
import { inject } from '@adonisjs/core'
import { hasPermission } from '#decorators/has_permission'
import {
  createRoleValidator,
  updateRoleValidator,
  assignPermissionsValidator,
} from '#validators/role'

@inject()
export default class RolesController extends BaseController {
  constructor(protected rolePermissionService: RolePermissionService) {
    super()
  }

  /**
   * GET /roles - Get all roles with their permissions (excluding super_admin)
   */
  @hasPermission('roles.read')
  async index({ request, response }: HttpContext) {
    const filters = this.extractFilters(request, ['search'])
    const pagination = this.getPaginationParams(request)

    const data = await this.rolePermissionService.getPaginatedRoles({ ...filters, ...pagination })

    return this.success(response, data, 'Roles retrieved successfully')
  }

  /**
   * GET /roles/:id - Get a specific role with its permissions
   */
  @hasPermission('roles.read')
  async show({ params, response }: HttpContext) {
    try {
      const role = await this.rolePermissionService.findById(params.id)

      if (!this.validateResourceExists(role, response, 'Role not found')) {
        return
      }

      return this.success(response, role, 'Role retrieved successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to retrieve role')
    }
  }

  /**
   * POST /roles - Create a new role
   */
  @hasPermission('roles.create')
  async store({ request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(createRoleValidator)
      const role = await this.rolePermissionService.createRole(payload)

      return this.success(response, role, 'Role created successfully', {}, 201)
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to create role')
    }
  }

  /**
   * PUT /roles/:id - Update a role
   */
  @hasPermission('roles.update')
  async update({ params, request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(updateRoleValidator)
      const role = await this.rolePermissionService.updateRole(params.id, payload)

      return this.success(response, role, 'Role updated successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to update role')
    }
  }

  /**
   * DELETE /roles/:id - Delete a role
   */
  @hasPermission('roles.delete')
  async destroy({ params, response }: HttpContext) {
    try {
      await this.rolePermissionService.deleteRole(params.id)
      return this.success(response, null, 'Role deleted successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to delete role')
    }
  }

  /**
   * GET /roles/permissions - Get all permissions
   */
  @hasPermission('roles.read')
  async permissions({ request, response }: HttpContext) {
    const filters = this.extractFilters(request, ['resource'])
    const pagination = this.getPaginationParams(request)

    const data = await this.rolePermissionService.getPaginatedPermissions({
      ...filters,
      ...pagination,
    })

    return this.success(response, data, 'Permissions retrieved successfully')
  }

  /**
   * POST /roles/:id/permissions - Assign permissions to a role
   */
  @hasPermission('roles.update')
  async assignPermissions({ params, request, response }: HttpContext) {
    try {
      const payload = await request.validateUsing(assignPermissionsValidator)
      const role = await this.rolePermissionService.assignPermissions(
        params.id,
        payload.permissionIds
      )

      return this.success(response, role, 'Permissions assigned successfully')
    } catch (error) {
      return this.handleError(response, error as Error, 'Failed to assign permissions')
    }
  }
}
