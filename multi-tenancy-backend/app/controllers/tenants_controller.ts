import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import BaseController from '#controllers/base_controller'

import {
  createTenantValidator,
  createTenantSimpleValidator,
  updateTenantValidator,
  updateTenantStatusValidator,
} from '#validators/tenant'
import TenantService from '#services/tenant_service'

@inject()
export default class TenantsController extends BaseController {
  constructor(protected tenantService: TenantService) {
    super()
  }

  /**
   * Get all tenants with filtering and pagination
   * Permission: tenants.read (checked by middleware)
   */
  async index({ request, response }: HttpContext) {
    return await this.handleIndex(
      request,
      response,
      this.tenantService,
      ['search', 'status'],
      'Tenants retrieved successfully'
    )
  }

  /**
   * Create a new tenant
   * Permission: tenants.create (checked by middleware)
   */
  async store({ request, response }: HttpContext) {
    return await this.handleStore(
      request,
      response,
      this.tenantService,
      createTenantValidator,
      'Tenant created successfully'
    )
  }

  /**
   * Create a new tenant with auto-generated slug (simplified creation)
   * The current user becomes the TENANT_OWNER automatically
   * Permission: tenants.create (checked by middleware)
   */
  async createSimple({ request, response, auth }: HttpContext) {
    try {
      const payload = await request.validateUsing(createTenantSimpleValidator)
      const user = auth.user!
      const tenant = await this.tenantService.createTenantSimple(payload.name, user)
      await this.tenantService.createDefaultBilling(tenant.id, user)

      return this.success(
        response,
        tenant,
        'Tenant created successfully and you have been assigned as the owner',
        {},
        201
      )
    } catch (error) {
      return this.handleError(response, error, 'Failed to create tenant')
    }
  }

  /**
   * Get a specific tenant
   * Permission: tenants.read (checked by middleware)
   */
  async show({ request, response }: HttpContext) {
    return await this.handleShow(
      request,
      response,
      this.tenantService,
      'Tenant retrieved successfully'
    )
  }

  /**
   * Update a tenant
   * Permission: tenants.update (checked by middleware)
   */
  async update({ request, response }: HttpContext) {
    return await this.handleUpdate(
      request,
      response,
      this.tenantService,
      updateTenantValidator,
      'Tenant updated successfully'
    )
  }

  /**
   * Update tenant status
   * Permission: tenants.manage (checked by middleware)
   */
  async updateStatus({ params, request, response }: HttpContext) {
    return this.handleAsync(
      async () => {
        const payload = await request.validateUsing(updateTenantStatusValidator)
        return await this.tenantService.updateTenantStatus(params.id, payload.status)
      },
      (tenant) =>
        this.success(
          response,
          {
            id: tenant.id,
            status: tenant.status,
            isActive: tenant.isActive,
            isOnTrial: tenant.isOnTrial,
            trialExpired: tenant.trialExpired,
          },
          'Tenant status updated successfully'
        ),
      (error) => this.handleError(response, error, 'Status update failed')
    )
  }
}
