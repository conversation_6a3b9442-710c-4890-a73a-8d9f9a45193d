import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import Permission from '#models/permission'
import { hasPermission } from '#decorators/has_permission'
import BaseController from '#controllers/base_controller'

@inject()
export default class UserPermissionsController extends BaseController {
  constructor() {
    super()
  }

  /**
   * Get available permissions for assignment
   */
  @hasPermission('users.read')
  async availablePermissions({ response }: HttpContext) {
    try {
      const permissions = await Permission.query()
        .where('isSystem', false) // Only non-system permissions can be assigned directly
        .orderBy('resource')
        .orderBy('action')

      return this.success(
        response,
        {
          permissions: permissions.map((permission) => ({
            id: permission.id,
            name: permission.name,
            displayName: permission.displayName,
            description: permission.description,
            resource: permission.resource,
            action: permission.action,
          })),
        },
        'Available permissions retrieved successfully'
      )
    } catch (error) {
      return this.error(response, 'Failed to retrieve available permissions', error.message)
    }
  }
}
