import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import BaseController from '#controllers/base_controller'
import PricingPlanService from '#services/pricing_plan_service'
import { BillingPlan, BillingCycle } from '#models/billing'

@inject()
export default class PricingPlansController extends BaseController {
  constructor(private pricingPlanService: PricingPlanService) {
    super()
  }

  /**
   * Get all active pricing plans
   */
  async index({ response }: HttpContext) {
    try {
      const plans = await this.pricingPlanService.getActivePlans()

      const formattedPlans = plans.map((plan) => ({
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        currency: plan.currency,
        isPopular: plan.isPopular,
        features: plan.featuresArray,
        limits: plan.limitsObject,
        trialDays: plan.trialDays,
        yearlySavings: plan.yearlySavingsPercentage,
        createdAt: plan.createdAt,
        updatedAt: plan.updatedAt,
      }))

      return this.success(response, formattedPlans, 'Pricing plans retrieved successfully')
    } catch (error) {
      return this.handleError(response, error, 'Failed to fetch pricing plans')
    }
  }

  /**
   * Get pricing plan by slug
   */
  async show({ params, response }: HttpContext) {
    try {
      const { slug } = params

      if (!Object.values(BillingPlan).includes(slug)) {
        return response.badRequest({
          success: false,
          message: 'Invalid plan slug',
        })
      }

      const plan = await this.pricingPlanService.getPlanBySlug(slug)

      if (!plan) {
        return response.notFound({
          success: false,
          message: 'Pricing plan not found',
        })
      }

      const formattedPlan = {
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        currency: plan.currency,
        isPopular: plan.isPopular,
        features: plan.featuresArray,
        limits: plan.limitsObject,
        trialDays: plan.trialDays,
        yearlySavings: plan.yearlySavingsPercentage,
        createdAt: plan.createdAt,
        updatedAt: plan.updatedAt,
      }

      return response.ok({
        success: true,
        data: formattedPlan,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch pricing plan',
        error: error.message,
      })
    }
  }

  /**
   * Get popular pricing plans
   */
  async popular({ response }: HttpContext) {
    try {
      const plans = await this.pricingPlanService.getPopularPlans()

      const formattedPlans = plans.map((plan) => ({
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        currency: plan.currency,
        isPopular: plan.isPopular,
        features: plan.featuresArray,
        limits: plan.limitsObject,
        trialDays: plan.trialDays,
        yearlySavings: plan.yearlySavingsPercentage,
      }))

      return response.ok({
        success: true,
        data: formattedPlans,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch popular pricing plans',
        error: error.message,
      })
    }
  }

  /**
   * Get plans comparison data
   */
  async comparison({ response }: HttpContext) {
    try {
      const comparison = await this.pricingPlanService.getPlansComparison()

      return response.ok({
        success: true,
        data: comparison,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch plans comparison',
        error: error.message,
      })
    }
  }

  /**
   * Get plan price for specific cycle
   */
  async price({ params, request, response }: HttpContext) {
    try {
      const { slug } = params
      const { cycle = BillingCycle.MONTHLY } = request.qs()

      if (!Object.values(BillingPlan).includes(slug)) {
        return response.badRequest({
          success: false,
          message: 'Invalid plan slug',
        })
      }

      if (!Object.values(BillingCycle).includes(cycle)) {
        return response.badRequest({
          success: false,
          message: 'Invalid billing cycle',
        })
      }

      const price = await this.pricingPlanService.getPlanPrice(slug, cycle)

      return response.ok({
        success: true,
        data: {
          slug,
          cycle,
          price,
          currency: 'usd',
        },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch plan price',
        error: error.message,
      })
    }
  }

  /**
   * Get plan features
   */
  async features({ params, response }: HttpContext) {
    try {
      const { slug } = params

      if (!Object.values(BillingPlan).includes(slug)) {
        return response.badRequest({
          success: false,
          message: 'Invalid plan slug',
        })
      }

      const features = await this.pricingPlanService.getPlanFeatures(slug)

      return response.ok({
        success: true,
        data: {
          slug,
          features,
        },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch plan features',
        error: error.message,
      })
    }
  }

  /**
   * Get upgrade options for current plan
   */
  async upgradeOptions({ params, response }: HttpContext) {
    try {
      const { slug } = params

      if (!Object.values(BillingPlan).includes(slug)) {
        return response.badRequest({
          success: false,
          message: 'Invalid plan slug',
        })
      }

      const upgradePlans = await this.pricingPlanService.getUpgradePlans(slug)
      const canUpgrade = await this.pricingPlanService.canUpgrade(slug)
      const nextUpgrade = await this.pricingPlanService.getNextUpgradePlan(slug)

      const formattedUpgradePlans = upgradePlans.map((plan) => ({
        id: plan.id,
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        monthlyPrice: plan.monthlyPrice,
        yearlyPrice: plan.yearlyPrice,
        currency: plan.currency,
        isPopular: plan.isPopular,
        features: plan.featuresArray,
        limits: plan.limitsObject,
        trialDays: plan.trialDays,
        yearlySavings: plan.yearlySavingsPercentage,
      }))

      return response.ok({
        success: true,
        data: {
          currentPlan: slug,
          canUpgrade,
          nextUpgrade: nextUpgrade
            ? {
                id: nextUpgrade.id,
                name: nextUpgrade.name,
                slug: nextUpgrade.slug,
                monthlyPrice: nextUpgrade.monthlyPrice,
                yearlyPrice: nextUpgrade.yearlyPrice,
              }
            : null,
          upgradePlans: formattedUpgradePlans,
        },
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch upgrade options',
        error: error.message,
      })
    }
  }
}
