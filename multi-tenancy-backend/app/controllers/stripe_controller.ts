import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import BaseController from '#controllers/base_controller'
import StripeService from '#services/stripe_service'
import StripeWebhookService from '#services/stripe_webhook_service'
import BillingService from '#services/billing_service'
import PricingPlanService from '#services/pricing_plan_service'

import { hasPermission } from '#decorators/has_permission'
import { BillingCycle } from '#models/billing'

@inject()
export default class StripeController extends BaseController {
  constructor(
    private stripeService: StripeService,
    private stripeWebhookService: StripeWebhookService,
    private billingService: BillingService,
    private pricingPlanService: PricingPlanService
  ) {
    super()
  }

  /**
   * Create subscription (primary method for SaaS billing)
   */
  @hasPermission('billing.create')
  async createSubscription({ request, response, logger, auth }: HttpContext) {
    const startTime = Date.now()

    try {
      const { priceId, customerEmail, customerName } = request.only([
        'priceId',
        'customerEmail',
        'customerName',
      ])

      logger.info('Creating Stripe subscription', {
        priceId,
        customerEmail,
        customerName,
        tenantId: request.tenant?.id,
      })

      if (!priceId || !customerEmail) {
        logger.warn('Subscription creation failed: Missing required fields', {
          priceId: !!priceId,
          customerEmail: !!customerEmail,
        })
        return response.badRequest({
          success: false,
          message: 'Price ID and customer email are required',
        })
      }

      const currentTenant = request.tenant
      if (!currentTenant) {
        logger.error('Subscription creation failed: No tenant context')
        return response.badRequest({
          success: false,
          message: 'No tenant context available',
        })
      }

      // Get or create Stripe customer
      let billing = await this.billingService.getByTenantId(currentTenant.id)
      let customerId = billing?.stripeCustomerId

      if (!customerId) {
        const customer = await this.stripeService.createCustomer(customerEmail, customerName)
        customerId = customer.id

        // Create or update billing record with customer ID
        if (billing) {
          await this.billingService.updateBilling(billing.id, {
            stripeCustomerId: customerId,
          })
        } else {
          // Create new billing record if none exists
          const planDetails = await this.pricingPlanService.getPlanByStripePriceId(priceId)
          if (!planDetails) {
            return response.badRequest({
              success: false,
              message: `Invalid price ID: ${priceId}`,
            })
          }

          const { plan, cycle } = planDetails
          const user = auth.getUserOrFail()
          billing = await this.billingService.createBilling(
            {
              tenantId: currentTenant.id,
              plan: plan.slug,
              cycle,
              amount: 0, // Will be updated by webhook
              currency: 'usd',
            },
            user
          )

          // Update with Stripe customer ID
          await this.billingService.updateBilling(billing.id, {
            stripeCustomerId: customerId,
            stripePriceId: priceId,
          })
        }
      }

      let subscription: any

      // Check if user already has an active subscription
      if (billing?.stripeSubscriptionId) {
        // Update existing subscription to new price (with automatic proration)
        subscription = await this.stripeService.updateSubscription(
          billing.stripeSubscriptionId,
          priceId
        )

        // Update billing with new price ID
        await this.billingService.updateBilling(billing.id, {
          stripeSubscriptionId: subscription.id,
          stripePriceId: priceId,
        })
      } else {
        // Create new subscription for first-time users
        subscription = await this.stripeService.createSubscription(
          customerId,
          priceId,
          currentTenant.id
        )

        // Update billing with subscription details
        if (billing) {
          await this.billingService.updateBilling(billing.id, {
            stripeSubscriptionId: subscription.id,
            stripePriceId: priceId,
          })
        }
      }

      const processingTime = Date.now() - startTime
      logger.info('Stripe subscription created successfully', {
        subscriptionId: subscription.id,
        customerId,
        tenantId: currentTenant.id,
        priceId,
        processingTimeMs: processingTime,
      })

      return response.ok({
        success: true,
        data: {
          subscriptionId: subscription.id,
          clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
          customerId,
        },
      })
    } catch (error) {
      const processingTime = Date.now() - startTime
      const { priceId, customerEmail } = request.only(['priceId', 'customerEmail'])

      logger.error('Stripe subscription creation failed', {
        priceId,
        customerEmail,
        tenantId: request.tenant?.id,
        processingTimeMs: processingTime,
        error: error.message,
        stack: error.stack,
      })

      return response.internalServerError({
        success: false,
        message: 'Failed to create subscription',
        error: error.message,
      })
    }
  }

  /**
   * Handle Stripe webhooks
   */
  async webhook({ request, response, logger }: HttpContext) {
    const startTime = Date.now()
    let eventId: string | undefined
    let eventType: string | undefined
    let event: any

    try {
      const payload = request.raw()
      const signature = request.header('stripe-signature')

      // Log webhook reception
      logger.info(
        {
          hasSignature: !!signature,
          payloadSize: payload?.length || 0,
          userAgent: request.header('user-agent'),
          ip: request.ip(),
        },
        'Stripe webhook received'
      )

      if (!signature || typeof signature !== 'string') {
        logger.warn('Stripe webhook rejected: Missing signature')
        return response.badRequest({
          success: false,
          message: 'Missing Stripe signature',
        })
      }

      // Verify webhook signature
      try {
        event = this.stripeService.verifyWebhookSignature(payload, signature as string)
        eventId = event.id
        eventType = event.type
      } catch (verificationError) {
        logger.error(
          {
            error: verificationError.message,
          },
          'Stripe webhook signature verification failed'
        )
        throw verificationError
      }

      // Handle different event types (focus on subscription events)
      switch (event.type) {
        case 'customer.subscription.created':
          logger.info(
            {
              eventId,
              subscriptionId: event.data.object.id,
              customerId: event.data.object.customer,
              status: event.data.object.status,
            },
            'Processing subscription created event'
          )
          await this.stripeWebhookService.handleSubscriptionSuccess(event.data.object as any)
          logger.info({ eventId }, 'Subscription created event processed successfully')
          break

        case 'customer.subscription.updated':
          logger.info(
            {
              eventId,
              subscriptionId: event.data.object.id,
              customerId: event.data.object.customer,
              status: event.data.object.status,
            },
            'Processing subscription updated event'
          )
          await this.stripeWebhookService.handleSubscriptionUpdated(event.data.object as any)
          logger.info({ eventId }, 'Subscription updated event processed successfully')
          break

        case 'customer.subscription.deleted':
          logger.info(
            {
              eventId,
              subscriptionId: event.data.object.id,
              customerId: event.data.object.customer,
              status: event.data.object.status,
            },
            'Processing subscription deleted event'
          )
          await this.stripeWebhookService.handleSubscriptionCanceled(event.data.object as any)
          logger.info({ eventId }, 'Subscription deleted event processed successfully')
          break

        case 'invoice.payment_succeeded':
          logger.info(
            {
              eventId,
              invoiceId: event.data.object.id,
              subscriptionId: event.data.object.subscription,
              customerId: event.data.object.customer,
              amount: event.data.object.amount_paid,
              currency: event.data.object.currency,
            },
            'Processing invoice payment succeeded event'
          )
          await this.stripeWebhookService.handleInvoicePaymentSuccess(event.data.object as any)
          logger.info({ eventId }, 'Invoice payment succeeded event processed successfully')
          break

        case 'invoice.payment_failed':
          logger.warn(
            {
              eventId,
              invoiceId: event.data.object.id,
              subscriptionId: event.data.object.subscription,
              customerId: event.data.object.customer,
              amount: event.data.object.amount_due,
              currency: event.data.object.currency,
              attemptCount: event.data.object.attempt_count,
            },
            'Processing invoice payment failed event'
          )
          await this.stripeWebhookService.handleInvoicePaymentFailed(event.data.object as any)
          logger.info({ eventId }, 'Invoice payment failed event processed successfully')
          break

        default:
          logger.info({ eventId, eventType }, 'Unhandled Stripe webhook event type')
      }

      const processingTime = Date.now() - startTime
      logger.info(
        {
          eventId,
          eventType,
          processingTimeMs: processingTime,
        },
        'Stripe webhook processed successfully'
      )

      return response.ok({
        success: true,
        message: 'Webhook processed successfully',
      })
    } catch (error) {
      const processingTime = Date.now() - startTime

      logger.error(
        {
          eventId,
          eventType,
          processingTimeMs: processingTime,
          error: error.message,
          stack: error.stack,
          // Add more context for debugging
          ...(error.type && { stripeErrorType: error.type }),
          ...(error.code && { stripeErrorCode: error.code }),
          ...(error.param && { stripeErrorParam: error.param }),
        },
        'Stripe webhook processing failed'
      )

      return response.badRequest({
        success: false,
        message: 'Webhook processing failed',
        error: error.message,
      })
    }
  }

  /**
   * Create Stripe Checkout session for subscription
   */
  @hasPermission('billing.create')
  async createCheckoutSession({ request, response, logger, auth }: HttpContext) {
    const startTime = Date.now()

    try {
      const { priceId, successUrl, cancelUrl } = request.only([
        'priceId',
        'successUrl',
        'cancelUrl',
      ])

      logger.info('Creating Stripe checkout session', {
        priceId,
        successUrl,
        cancelUrl,
        tenantId: request.tenant?.id,
      })

      if (!priceId || !successUrl || !cancelUrl) {
        logger.warn('Checkout session creation failed: Missing required fields', {
          priceId: !!priceId,
          successUrl: !!successUrl,
          cancelUrl: !!cancelUrl,
        })
        return response.badRequest({
          success: false,
          message: 'Price ID, success URL, and cancel URL are required',
        })
      }

      const currentTenant = request.tenant
      if (!currentTenant) {
        logger.error('Checkout session creation failed: No tenant context')
        return response.badRequest({
          success: false,
          message: 'No tenant context available',
        })
      }

      // Get user email from auth context
      const user = auth.user!
      const userEmail = user.email
      if (!userEmail) {
        logger.error('Checkout session creation failed: No user email available')
        return response.badRequest({
          success: false,
          message: 'No user email available',
        })
      }

      // Check if tenant already has billing/customer
      const billing = await this.billingService.getByTenantId(currentTenant.id)
      const existingCustomerId = billing?.stripeCustomerId || undefined

      // Create checkout session
      const session = await this.stripeService.createCheckoutSession(
        priceId,
        userEmail,
        currentTenant.id,
        successUrl,
        cancelUrl,
        existingCustomerId
      )

      const processingTime = Date.now() - startTime
      logger.info('Stripe checkout session created successfully', {
        sessionId: session.id,
        sessionUrl: session.url,
        tenantId: currentTenant.id,
        priceId,
        processingTimeMs: processingTime,
      })

      return response.ok({
        success: true,
        data: {
          sessionId: session.id,
          url: session.url,
        },
      })
    } catch (error) {
      const processingTime = Date.now() - startTime
      const { priceId } = request.only(['priceId'])

      logger.error('Stripe checkout session creation failed', {
        priceId,
        tenantId: request.tenant?.id,
        processingTimeMs: processingTime,
        error: error.message,
        stack: error.stack,
      })

      return response.internalServerError({
        success: false,
        message: 'Failed to create checkout session',
        error: error.message,
      })
    }
  }

  /**
   * Update existing subscription to new plan
   */
  @hasPermission('billing.update')
  async updateSubscription({ request, response, logger, auth }: HttpContext) {
    const startTime = Date.now()

    try {
      const { priceId } = request.only(['priceId'])
      const user = auth.user!
      const currentTenant = request.tenant
      const pricingPlan = await this.pricingPlanService.getPlanByStripePriceId(priceId)
      logger.info('Updating subscription', {
        priceId,
        tenantId: currentTenant.id,
        userId: user.id,
      })

      // Get current billing info
      const billing = await this.billingService.getByTenantId(currentTenant.id)
      if (!billing?.stripeSubscriptionId) {
        logger.error('Subscription update failed: No active subscription found')
        return response.badRequest({
          success: false,
          message: 'No active subscription found',
        })
      }

      // Update the subscription
      const updatedSubscription = await this.stripeService.updateSubscription(
        billing.stripeSubscriptionId,
        priceId
      )

      const processingTime = Date.now() - startTime
      logger.info('Subscription updated successfully', {
        subscriptionId: updatedSubscription.id,
        newPriceId: priceId,
        tenantId: currentTenant.id,
        processingTimeMs: processingTime,
      })
      // update the billing record
      this.billingService.updateBilling(billing.id, {
        stripePriceId: priceId,
        stripeSubscriptionId: updatedSubscription.id,
        plan: pricingPlan?.plan.slug,
        cycle: pricingPlan?.cycle,
        amount:
          pricingPlan?.cycle === BillingCycle.YEARLY
            ? pricingPlan?.plan.yearlyPrice
            : pricingPlan?.plan.monthlyPrice,
      })

      return response.ok({
        success: true,
        data: {
          subscriptionId: updatedSubscription.id,
          status: updatedSubscription.status,
        },
        message: 'Subscription updated successfully',
      })
    } catch (error: any) {
      const processingTime = Date.now() - startTime
      logger.error('Subscription update failed', {
        error: error.message,
        stack: error.stack,
        tenantId: request.tenant?.id,
        processingTimeMs: processingTime,
      })

      return response.internalServerError({
        success: false,
        message: 'Failed to update subscription',
        error: error.message,
      })
    }
  }

  /**
   * Preview subscription change (proration calculation)
   */
  @hasPermission('billing.read')
  async previewSubscriptionChange({ request, response, logger, auth }: HttpContext) {
    const startTime = Date.now()

    try {
      const { priceId } = request.only(['priceId'])
      const user = auth.user!
      const currentTenant = request.tenant

      logger.info('Previewing subscription change', {
        priceId,
        tenantId: currentTenant.id,
        userId: user.id,
      })

      // Get current billing info
      const billing = await this.billingService.getByTenantId(currentTenant.id)
      if (!billing?.stripeSubscriptionId || !billing?.stripeCustomerId) {
        logger.error('Preview failed: No active subscription found')
        return response.badRequest({
          success: false,
          message: 'No active subscription found',
        })
      }

      // Preview the subscription change
      const upcomingInvoice = await this.stripeService.previewSubscriptionChange(
        billing.stripeCustomerId,
        billing.stripeSubscriptionId,
        priceId
      )

      const processingTime = Date.now() - startTime
      logger.info('Subscription change preview generated', {
        subscriptionId: billing.stripeSubscriptionId,
        newPriceId: priceId,
        tenantId: currentTenant.id,
        processingTimeMs: processingTime,
      })

      return response.ok({
        success: true,
        data: {
          amountDue: upcomingInvoice.amount_due,
          currency: upcomingInvoice.currency,
          total: upcomingInvoice.total,
          subtotal: upcomingInvoice.subtotal,
          lines: upcomingInvoice.lines.data.map((line) => ({
            description: line.description,
            amount: line.amount,
            proration: line.proration,
          })),
        },
      })
    } catch (error: any) {
      const processingTime = Date.now() - startTime
      logger.error('Subscription change preview failed', {
        error: error.message,
        stack: error.stack,
        tenantId: request.tenant?.id,
        processingTimeMs: processingTime,
      })

      return response.internalServerError({
        success: false,
        message: 'Failed to preview subscription change',
        error: error.message,
      })
    }
  }

  /**
   * Create Stripe customer portal session
   */
  @hasPermission('billing.read')
  async createPortalSession({ request, response, logger }: HttpContext) {
    const startTime = Date.now()

    try {
      logger.info('Creating Stripe customer portal session', {
        tenantId: request.tenant?.id,
        origin: request.header('origin'),
      })
      const currentTenant = request.tenant
      if (!currentTenant) {
        return response.badRequest({
          success: false,
          message: 'No tenant context available',
        })
      }

      // Get billing record to find Stripe customer ID
      const billing = await this.billingService.getByTenantId(currentTenant.id)
      if (!billing || !billing.stripeCustomerId) {
        logger.warn('Portal session creation failed: No Stripe customer found', {
          tenantId: currentTenant.id,
          hasBilling: !!billing,
          hasCustomerId: !!billing?.stripeCustomerId,
        })
        return response.badRequest({
          success: false,
          message: 'No Stripe customer found for this tenant',
        })
      }

      logger.debug('Creating Stripe customer portal session', {
        customerId: billing.stripeCustomerId,
        returnUrl: `${request.header('origin')}/billing`,
      })

      // Create customer portal session
      const session = await this.stripeService.createCustomerPortalSession(
        billing.stripeCustomerId,
        `${request.header('origin')}/billing` // Return URL
      )

      const processingTime = Date.now() - startTime
      logger.info('Stripe customer portal session created successfully', {
        sessionId: session.id,
        customerId: billing.stripeCustomerId,
        tenantId: currentTenant.id,
        processingTimeMs: processingTime,
      })

      return response.ok({
        success: true,
        data: {
          url: session.url,
        },
      })
    } catch (error) {
      const processingTime = Date.now() - startTime
      logger.error('Stripe customer portal session creation failed', {
        tenantId: request.tenant?.id,
        processingTimeMs: processingTime,
        error: error.message,
        stack: error.stack,
      })

      return response.internalServerError({
        success: false,
        message: 'Failed to create portal session',
        error: error.message,
      })
    }
  }
}
