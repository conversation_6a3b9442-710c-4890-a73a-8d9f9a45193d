import type { HttpContext } from '@adonisjs/core/http'
import { inject } from '@adonisjs/core'
import BaseController from '#controllers/base_controller'
import BillingService from '#services/billing_service'
import { hasPermission } from '#decorators/has_permission'
import TenantContext from '#services/tenant_context'

@inject()
export default class BillingController extends BaseController {
  constructor(private billingService: BillingService) {
    super()
  }

  /**
   * Get current tenant's billing (tenant-scoped)
   */
  @hasPermission('billing.read')
  async current({ response }: HttpContext) {
    // Get tenant from tenant context
    const tenantId = TenantContext.getTenantId()

    if (!tenantId) {
      return response.badRequest({
        success: false,
        message: 'No tenant context found',
      })
    }

    const billing = await this.billingService.getByTenantId(tenantId)

    // Return 200 with null data instead of 404 when billing not found
    return response.ok({
      success: true,
      data: billing || null,
    })
  }
}
