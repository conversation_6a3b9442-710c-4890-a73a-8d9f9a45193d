import { ResponseHelper } from '#utils/response_helper'

export default abstract class BaseController {
  /**
   * Handle async operations with automatic error handling
   */
  protected async handleAsync<T>(
    operation: () => Promise<T>,
    onSuccess: (result: T) => any,
    onError?: (error: Error) => any
  ): Promise<any> {
    try {
      const result = await operation()
      return onSuccess(result)
    } catch (error) {
      if (onError) {
        return onError(error)
      }
      throw error
    }
  }

  /**
   * Standardized success response
   */
  protected success<T>(
    response: any,
    data: T,
    message: string,
    meta: Record<string, any> = {},
    statusCode = 200
  ) {
    return ResponseHelper.success(response, data, message, meta, statusCode)
  }

  /**
   * Standardized error response
   */
  protected error(response: any, message: string, error: string, statusCode = 400) {
    return ResponseHelper.error(response, message, error, statusCode)
  }

  /**
   * Standardized paginated response
   */
  protected paginated<T>(
    response: any,
    data: T,
    message: string,
    pagination: {
      currentPage: number
      perPage: number
      total: number
      lastPage: number
      hasMorePages: boolean
      hasPages: boolean
    },
    statusCode = 200
  ) {
    return ResponseHelper.paginated(response, data, message, pagination, statusCode)
  }

  /**
   * Handle common errors with appropriate status codes
   */
  protected handleError(response: any, error: Error, defaultMessage = 'Operation failed') {
    return ResponseHelper.handleError(response, error, defaultMessage)
  }

  /**
   * Extract pagination parameters from request
   */
  protected getPaginationParams(request: any) {
    return {
      page: request.input('page', 1),
      limit: request.input('limit', 10),
    }
  }

  /**
   * Extract search parameters from request
   */
  protected getSearchParams(request: any, searchFields: string[] = []) {
    const params: Record<string, any> = {}

    searchFields.forEach((field) => {
      const value = request.input(field)
      if (value) {
        params[field] = value
      }
    })

    return params
  }

  /**
   * Extract filter parameters from request
   */
  protected extractFilters(request: any, filterFields: string[] = []) {
    const filters: Record<string, any> = {}

    filterFields.forEach((field) => {
      const value = request.input(field)
      if (value !== undefined && value !== null && value !== '') {
        filters[field] = value
      }
    })

    return filters
  }

  /**
   * Validate user permissions for a resource
   */
  protected async validatePermission(
    user: any,
    permissionCheck: () => Promise<boolean>,
    response: any,
    errorMessage = 'Access denied'
  ): Promise<boolean> {
    const hasPermission = await permissionCheck()

    if (!hasPermission) {
      this.error(response, errorMessage, 'Insufficient permissions', 403)
      return false
    }

    return true
  }

  /**
   * Validate resource exists
   */
  protected validateResourceExists<T>(
    resource: T | null,
    response: any,
    errorMessage = 'Resource not found'
  ): resource is T {
    if (!resource) {
      this.error(response, errorMessage, 'Resource with the specified ID does not exist', 404)
      return false
    }

    return true
  }

  /**
   * Standard CRUD index method - can be overridden by controllers
   */
  protected async handleIndex(
    request: any,
    response: any,
    service: any,
    filterFields: string[] = [],
    message = 'Records retrieved successfully'
  ) {
    try {
      const filters = this.extractFilters(request, filterFields)
      const pagination = this.getPaginationParams(request)

      const result = await service.getPaginated({ ...pagination, ...filters })
      return this.success(response, result, message)
    } catch (error) {
      return this.handleError(response, error)
    }
  }

  /**
   * Standard CRUD show method - can be overridden by controllers
   */
  protected async handleShow(
    request: any,
    response: any,
    service: any,
    message = 'Record retrieved successfully'
  ) {
    try {
      const id = request.param('id')
      const record = await service.findOrFail(id)
      return this.success(response, record, message)
    } catch (error) {
      return this.handleError(response, error)
    }
  }

  /**
   * Standard CRUD store method - can be overridden by controllers
   */
  protected async handleStore(
    request: any,
    response: any,
    service: any,
    validator: any,
    message = 'Record created successfully'
  ) {
    try {
      const data = await request.validateUsing(validator)
      const record = await service.create(data)
      return this.success(response, record, message, {}, 201)
    } catch (error) {
      return this.handleError(response, error)
    }
  }

  /**
   * Standard CRUD update method - can be overridden by controllers
   */
  protected async handleUpdate(
    request: any,
    response: any,
    service: any,
    validator: any,
    message = 'Record updated successfully'
  ) {
    try {
      const id = request.param('id')
      const data = await request.validateUsing(validator)
      const record = await service.update(id, data)
      return this.success(response, record, message)
    } catch (error) {
      return this.handleError(response, error)
    }
  }

  /**
   * Standard CRUD destroy method - can be overridden by controllers
   */
  protected async handleDestroy(
    request: any,
    response: any,
    service: any,
    message = 'Record deleted successfully'
  ) {
    try {
      const id = request.param('id')
      await service.delete(id)
      return this.success(response, null, message, {}, 204)
    } catch (error) {
      return this.handleError(response, error)
    }
  }
}
