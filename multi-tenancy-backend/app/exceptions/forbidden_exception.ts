import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'

export default class ForbiddenException extends Exception {
  static status = 403
  static code = 'E_FORBIDDEN'

  constructor(message: string = 'Access denied', errorDetails?: string) {
    super(message, { status: 403, code: 'E_FORBIDDEN' })
    this.message = message
    if (errorDetails) {
      this.cause = errorDetails
    }
  }

  async handle(error: this, ctx: HttpContext) {
    return ctx.response.status(403).json({
      success: false,
      message: error.message,
      error: error.cause || 'Insufficient permissions',
    })
  }
}
