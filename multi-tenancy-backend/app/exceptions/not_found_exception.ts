import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'

export default class NotFoundException extends Exception {
  static status = 404
  static code = 'E_NOT_FOUND'

  constructor(message: string = 'Resource not found', errorDetails?: string) {
    super(message, { status: 404, code: 'E_NOT_FOUND' })
    this.message = message
    if (errorDetails) {
      this.cause = errorDetails
    }
  }

  async handle(error: this, ctx: HttpContext) {
    return ctx.response.status(404).json({
      success: false,
      message: error.message,
      error: error.cause || 'The requested resource was not found',
    })
  }
}
