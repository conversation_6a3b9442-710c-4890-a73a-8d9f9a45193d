import Tenant from '#models/tenant'
import { DateTime } from 'luxon'

// add tenantId to request
declare module '@adonisjs/core/http' {
  interface Request {
    tenant: Tenant
  }
}

/**
 * Base filters interface for pagination and search
 */
export interface BaseFilters {
  page?: number
  limit?: number
  search?: string
  [key: string]: any
}

/**
 * Base pagination result interface
 */
export interface BasePaginationResult<T> {
  data: T[]
  pagination: {
    currentPage: number
    perPage: number
    total: number
    lastPage: number
    hasMorePages: boolean
    hasPages: boolean
  }
}

/**
 * Common service response interface
 */
export interface ServiceResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

/**
 * Common service error interface
 */
export interface ServiceError {
  message: string
  code?: string
  field?: string
}

/**
 * Base model interface for common fields
 */
export interface BaseModelFields {
  id: number
  createdAt: DateTime
  updatedAt: DateTime
}

/**
 * Audit fields interface
 */
export interface AuditFields {
  createdBy?: number
  updatedBy?: number
  deletedAt?: DateTime | null
  deletedBy?: number
}
