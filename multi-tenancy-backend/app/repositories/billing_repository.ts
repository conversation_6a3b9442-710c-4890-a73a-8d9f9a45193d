import { inject } from '@adonisjs/core'
import Billing from '#models/billing'
import BaseRepository from '#repositories/base_repository'

/**
 * Repository for billing database operations with security features
 */
@inject()
export default class BillingRepository extends BaseRepository<typeof Billing> {
  protected model = Billing

  // Security: Define allowed fields for updates
  protected allowedUpdateFields = [
    'plan',
    'cycle',
    'amount',
    'currency',
    'maxUsers',
    'maxStorage',
    'stripeCustomerId',
    'stripeSubscriptionId',
    'stripePriceId',
    'status',
    'currentPeriodStart',
    'currentPeriodEnd',
    'cancelledAt',
    'features',
  ]

  // Security: Define sensitive fields that should never be updated
  protected sensitiveFields = [
    'id',
    'tenantId', // Prevent tenant switching
    'created_at',
    'updated_at',
  ]
  /**
   * Find billing by tenant ID
   */
  async findByTenantId(tenantId: number): Promise<Billing | null> {
    return await this.findBy('tenant_id', tenantId)
  }

  /**
   * Get billing records by status
   */
  async findByStatus(status: string): Promise<Billing[]> {
    return await Billing.query().where('status', status)
  }

  /**
   * Get billing records that need upgrade
   */
  async findNeedingUpgrade(): Promise<Billing[]> {
    return await Billing.query().where('needs_upgrade', true)
  }

  /**
   * Update needs upgrade status
   */
  async updateNeedsUpgrade(id: number, needsUpgrade: boolean): Promise<void> {
    await Billing.query().where('id', id).update({ needs_upgrade: needsUpgrade })
  }

  /**
   * Get billing records with expired periods
   */
  async findExpired(): Promise<Billing[]> {
    return await Billing.query().where('current_period_end', '<', new Date())
  }

  /**
   * Get billing records expiring soon
   */
  async findExpiringSoon(days: number = 7): Promise<Billing[]> {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + days)

    return await Billing.query()
      .where('current_period_end', '>', new Date())
      .where('current_period_end', '<=', futureDate)
  }

  /**
   * Get billing records with expiring trials
   */
  async findExpiringTrials(days: number = 7): Promise<Billing[]> {
    const expiryDate = new Date()
    expiryDate.setDate(expiryDate.getDate() + days)

    return await this.getQuery()
      .whereNotNull('trial_end')
      .where('trial_end', '<=', expiryDate)
      .where('trial_end', '>', new Date())
      .where('status', 'active')
  }

  /**
   * Get query builder for complex filtering (public method for service layer)
   */
  getQueryBuilder() {
    return this.getQuery()
  }
}
