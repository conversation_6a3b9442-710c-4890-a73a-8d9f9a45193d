import { inject } from '@adonisjs/core'
import Permission from '#models/permission'
import BaseRepository, {
  BasePaginationFilters,
  BasePaginationResult,
} from '#repositories/base_repository'

export interface PermissionFilters extends BasePaginationFilters {
  search?: string
  resource?: string
  action?: string
}

/**
 * Repository for permission database operations with security features
 */
@inject()
export default class PermissionRepository extends BaseRepository<typeof Permission> {
  protected model = Permission
  protected searchFields = ['name', 'resource', 'action', 'description']

  // Security: Define allowed fields for updates
  protected allowedUpdateFields = ['name', 'resource', 'action', 'description']

  // Security: Define sensitive fields that should never be updated
  protected sensitiveFields = ['id', 'created_at', 'updated_at']

  /**
   * Get paginated permissions with filtering
   */
  async getPaginatedPermissions(
    filters: PermissionFilters = {}
  ): Promise<BasePaginationResult<Permission>> {
    const { page = 1, limit = 10, search, resource, action } = filters

    const query = this.getQuery()

    // Apply search filter
    if (search) {
      query.where((builder) => {
        builder
          .where('name', 'like', `%${search}%`)
          .orWhere('resource', 'like', `%${search}%`)
          .orWhere('action', 'like', `%${search}%`)
          .orWhere('description', 'like', `%${search}%`)
      })
    }

    // Apply resource filter
    if (resource) {
      query.where('resource', resource)
    }

    // Apply action filter
    if (action) {
      query.where('action', action)
    }

    const permissions = await query.paginate(page, limit)

    return {
      data: permissions.all(),
      pagination: {
        currentPage: permissions.currentPage,
        perPage: permissions.perPage,
        total: permissions.total,
        lastPage: permissions.lastPage,
        hasMorePages: permissions.hasMorePages,
        hasPages: permissions.hasPages,
      },
    }
  }

  /**
   * Find permission by name
   */
  async findByName(name: string): Promise<Permission | null> {
    return await this.findBy('name', name)
  }

  /**
   * Find permission by name or throw error
   */
  async findByNameOrFail(name: string): Promise<Permission> {
    return await this.findByOrFail('name', name)
  }

  /**
   * Find permissions by resource
   */
  async findByResource(resource: string): Promise<Permission[]> {
    return await this.getQuery().where('resource', resource).orderBy('action')
  }

  /**
   * Find permissions by resource and action
   */
  async findByResourceAndAction(resource: string, action: string): Promise<Permission | null> {
    return await this.getQuery().where('resource', resource).where('action', action).first()
  }

  /**
   * Get all unique resources
   */
  async getUniqueResources(): Promise<string[]> {
    const results = await this.getQuery().distinct('resource').orderBy('resource')

    return results.map((permission) => permission.resource)
  }

  /**
   * Get all unique actions for a resource
   */
  async getActionsForResource(resource: string): Promise<string[]> {
    const results = await this.getQuery()
      .select('action')
      .where('resource', resource)
      .orderBy('action')

    return results.map((permission) => permission.action)
  }

  /**
   * Check if permission exists by name
   */
  async existsByName(name: string): Promise<boolean> {
    const permission = await this.findByName(name)
    return !!permission
  }

  /**
   * Get all permission names
   */
  async getAllPermissionNames(): Promise<string[]> {
    const permissions = await this.getQuery().select('name')
    return permissions.map((permission) => permission.name)
  }
}
