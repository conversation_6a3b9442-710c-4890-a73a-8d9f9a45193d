import { inject } from '@adonisjs/core'
import User, { UserStatus } from '#models/user'
import BaseRepository, { BasePaginationFilters } from './base_repository.js'
import { BasePaginationResult } from '../types/service.js'

export interface CreateUserData {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  status?: UserStatus
}

export interface UpdateUserData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  status?: UserStatus
  password?: string
}

export interface UserFilters extends BasePaginationFilters {
  status?: UserStatus
  tenantId?: number
}

@inject()
export default class UserRepository extends BaseRepository<typeof User> {
  protected model = User
  protected searchFields = ['firstName', 'lastName', 'email']

  async getPaginated(
    filters: UserFilters = {},
    tenantId?: number
  ): Promise<BasePaginationResult<User>> {
    if (tenantId) {
      this.filterTenant(tenantId)
    }
    return await super.getPaginated(filters)
  }

  filterTenant(tenantId: number): UserRepository {
    const query = this.getQuery().whereHas('tenants', (builder) => {
      builder.where('tenant_id', tenantId)
    })
    this.setQuery(query)
    return this
  }
  /**
   * Find user by email using normalized comparison
   */
  async findByEmail(email: string): Promise<User | null> {
    return await User.findByNormalizedEmail(email)
  }

  /**
   * Create a new user with user-specific logic
   */
  async create(data: CreateUserData): Promise<User> {
    const userData = {
      firstName: data.firstName,
      lastName: data.lastName,
      fullName: `${data.firstName} ${data.lastName}`,
      email: data.email,
      password: data.password,
      phone: data.phone || null,
      status: data.status || UserStatus.ACTIVE,
    }

    return await super.create(userData)
  }

  /**
   * Update an existing user with user-specific logic
   */
  async update(id: number, data: UpdateUserData): Promise<User> {
    const user = await this.findByIdOrFail(id)

    // Prepare update data
    const updateData: Partial<User> = {}
    if (data.firstName !== undefined) updateData.firstName = data.firstName
    if (data.lastName !== undefined) updateData.lastName = data.lastName
    if (data.email !== undefined) updateData.email = data.email
    if (data.phone !== undefined) updateData.phone = data.phone
    if (data.status !== undefined) updateData.status = data.status
    if (data.password !== undefined) updateData.password = data.password

    // Update fullName if either firstName or lastName is being updated
    if (data.firstName !== undefined || data.lastName !== undefined) {
      const firstName = data.firstName || user.firstName
      const lastName = data.lastName || user.lastName
      updateData.fullName = `${firstName} ${lastName}`
    }

    return await super.update(id, updateData)
  }

  /**
   * Check if user exists by email in a specific tenant
   */
  async existsInTenant(email: string, tenantId: number): Promise<boolean> {
    const user = await this.findByEmail(email)
    if (!user) {
      return false
    }

    const existingUserInTenant = await this.getQuery()
      .where('id', user.id)
      .whereHas('tenants', (builder) => {
        builder.where('tenant_id', tenantId)
      })
      .first()

    return !!existingUserInTenant
  }

  /**
   * Get users with pagination and filtering (extends base getAll with user-specific filters)
   */
  async getUsers(filters: UserFilters = {}) {
    const { tenantId, ...baseFilters } = filters

    // Start with base query
    const query = this.getQuery()

    // Apply tenant filter if provided (user-specific)
    if (tenantId) {
      query.whereHas('tenants', (builder) => {
        builder.where('tenant_id', tenantId)
      })
    }

    // Use base getAll for common pagination and search logic
    // But we need to apply tenant filter first, so we'll implement it here
    const { page = 1, limit = 10, search, status } = baseFilters

    // Apply search filter
    if (search && this.searchFields.length > 0) {
      query.where((builder) => {
        this.searchFields.forEach((field, index) => {
          if (index === 0) {
            builder.where(field, 'like', `%${search}%`)
          } else {
            builder.orWhere(field, 'like', `%${search}%`)
          }
        })
      })
    }

    // Apply status filter
    if (status) {
      query.where('status', status)
    }

    // Apply pagination and get results
    const users = await query.orderBy('created_at', 'desc').paginate(page, limit)

    return {
      data: users.all(),
      pagination: {
        currentPage: users.currentPage,
        perPage: users.perPage,
        total: users.total,
        lastPage: users.lastPage,
        hasMorePages: users.hasMorePages,
        hasPages: users.hasPages,
      },
    }
  }

  /**
   * Find users by tenant ID
   */
  async findByTenant(tenantId: number): Promise<User[]> {
    return (await this.getQuery().whereHas('tenants', (builder) => {
      builder.where('tenant_id', tenantId)
    })) as User[]
  }

  /**
   * Find users by status
   */
  async findByStatus(status: UserStatus): Promise<User[]> {
    return await this.findWhere({ status })
  }
}
