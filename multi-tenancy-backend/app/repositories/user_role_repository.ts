import { inject } from '@adonisjs/core'
import UserRole from '#models/user_role'
import BaseRepository from '#repositories/base_repository'
import User from '#models/user'

@inject()
export default class UserRoleRepository extends BaseRepository<typeof UserRole> {
  protected model = UserRole
  protected searchFields = ['name', 'slug']
  protected allowedUpdateFields = ['userId', 'roleId', 'tenantId']

  /**
   * Get all user roles for a user (optionally filtered by tenant)
   */
  async getUserRoles(user: User, tenantId?: number | null): Promise<UserRole[]> {
    return await this.getQuery()
      .where('userId', user.id)
      .if(tenantId !== undefined, (query) => {
        if (tenantId === null) {
          query.whereNull('tenantId')
        } else if (tenantId !== undefined) {
          query.where('tenantId', tenantId)
        }
      })
      .preload('role')
  }

  /**
   * Check if user role assignment already exists
   */
  async findExistingAssignment(
    userId: number,
    roleId: number,
    tenantId?: number | null
  ): Promise<UserRole | null> {
    return await this.getQuery()
      .where('userId', userId)
      .where('roleId', roleId)
      .if(tenantId === null, (query) => query.whereNull('tenantId'))
      .if(tenantId !== null && tenantId !== undefined, (query) =>
        query.where('tenantId', tenantId!)
      )
      .first()
  }

  /**
   * Remove user role assignment
   */
  async removeUserRole(userId: number, roleId: number, tenantId?: number | null): Promise<void> {
    await this.getQuery()
      .where('userId', userId)
      .where('roleId', roleId)
      .if(tenantId === null, (query) => query.whereNull('tenantId'))
      .if(tenantId !== null && tenantId !== undefined, (query) =>
        query.where('tenantId', tenantId!)
      )
      .delete()
  }

  /**
   * Remove all roles from a user (optionally within a specific tenant)
   */
  async removeAllUserRoles(userId: number, tenantId?: number | null): Promise<void> {
    const query = this.getQuery().where('userId', userId)

    if (tenantId !== undefined) {
      if (tenantId === null) {
        query.whereNull('tenantId')
      } else {
        query.where('tenantId', tenantId)
      }
    }

    await query.delete()
  }

  /**
   * Bulk create user role assignments
   */
  async bulkCreateUserRoles(
    assignments: Array<{ userId: number; roleId: number; tenantId?: number | null }>
  ): Promise<UserRole[]> {
    const records: UserRole[] = []

    for (const assignment of assignments) {
      const record = await this.create(assignment)
      records.push(record)
    }

    return records
  }

  /**
   * Get user roles with role details
   */
  async getUserRolesWithDetails(user: User, tenantId?: number | null): Promise<any[]> {
    const userRoles = await this.getUserRoles(user, tenantId)
    return userRoles.map((userRole) => userRole.role)
  }

  /**
   * Check if user has specific role
   */
  async hasUserRole(userId: number, roleName: string, tenantId?: number | null): Promise<boolean> {
    const userRole = await this.getQuery()
      .where('userId', userId)
      .whereHas('role', (roleQuery) => {
        roleQuery.where('name', roleName)
      })
      .if(tenantId !== undefined, (query) => {
        if (tenantId === null) {
          query.whereNull('tenantId')
        } else if (tenantId !== undefined) {
          query.where('tenantId', tenantId!)
        }
      })
      .first()

    return userRole !== null
  }

  /**
   * Find user role assignment
   */
  async findUserRole(
    userId: number,
    roleId: number,
    tenantId?: number | null
  ): Promise<UserRole | null> {
    return await this.findExistingAssignment(userId, roleId, tenantId)
  }

  /**
   * Assign role to user
   */
  async assignRole(userId: number, roleId: number, tenantId?: number | null): Promise<UserRole> {
    return await this.create({
      userId,
      roleId,
      tenantId,
    })
  }

  /**
   * Get user permissions from roles
   */
  async getUserPermissions(userId: number, tenantId?: number | null): Promise<string[]> {
    const userRoles = await this.getQuery()
      .where('userId', userId)
      .if(tenantId !== undefined, (query) => {
        if (tenantId === null) {
          query.whereNull('tenantId')
        } else if (tenantId !== undefined) {
          query.where('tenantId', tenantId!)
        }
      })
      .preload('role', (roleQuery) => {
        roleQuery.preload('permissions')
      })

    const permissions: string[] = []
    for (const userRole of userRoles) {
      if (userRole.role && userRole.role.permissions) {
        for (const permission of userRole.role.permissions) {
          permissions.push(permission.name)
        }
      }
    }

    return [...new Set(permissions)] // Remove duplicates
  }
}
