import { inject } from '@adonisjs/core'
import { BaseModel } from '@adonisjs/lucid/orm'
import { ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'

export interface BasePaginationFilters {
  page?: number
  limit?: number
  search?: string
}

export interface BasePaginationResult<T> {
  data: T[]
  pagination: {
    currentPage: number
    perPage: number
    total: number
    lastPage: number
    hasMorePages: boolean
    hasPages: boolean
  }
}

@inject()
export default abstract class BaseRepository<T extends typeof BaseModel> {
  protected abstract model: T
  protected searchFields: string[] = []

  // Security: Define allowed fields for updates (override in child classes)
  protected allowedUpdateFields: string[] = []

  // Security: Define sensitive fields that should never be updated (override in child classes)
  protected sensitiveFields: string[] = ['id', 'created_at', 'updated_at']

  /**
   * Get the query builder
   */

  protected query: ModelQueryBuilderContract<T, InstanceType<T>> | null = null
  protected setQuery(query: ModelQueryBuilderContract<T, InstanceType<T>>) {
    this.query = query
  }

  protected getQuery(): ModelQueryBuilderContract<T, InstanceType<T>> {
    if (this.query) {
      return this.query
    }
    return this.model.query()
  }

  /**
   * Find record by ID
   */
  async findById(id: number): Promise<InstanceType<T> | null> {
    return (await this.getQuery().where('id', id).first()) as InstanceType<T> | null
  }

  /**
   * Find record by ID or throw error
   */
  async findByIdOrFail(id: number): Promise<InstanceType<T>> {
    const record = await this.findById(id)
    if (!record) {
      throw new Error(`${this.model.name} not found`)
    }
    return record
  }

  /**
   * Find record by field
   */
  async findBy(field: string, value: any): Promise<InstanceType<T> | null> {
    return (await this.getQuery().where(field, value).first()) as InstanceType<T> | null
  }

  /**
   * Find record by field or throw error
   */
  async findByOrFail(field: string, value: any): Promise<InstanceType<T>> {
    const record = await this.findBy(field, value)
    if (!record) {
      throw new Error(`${this.model.name} not found`)
    }
    return record
  }

  /**
   * Create a new record
   */
  async create(data: Partial<InstanceType<T>>): Promise<InstanceType<T>> {
    const record = new this.model()

    // Assign all provided data to the record
    Object.assign(record, data)

    await record.save()
    return record as InstanceType<T>
  }

  /**
   * Update an existing record with security checks
   */
  async update(id: number, data: any): Promise<InstanceType<T>> {
    const record = await this.findByIdOrFail(id)

    // Transform and sanitize update data using whitelist approach
    const updateData = this.transformUpdateData(data)

    // Update fields
    Object.assign(record, updateData)

    await record.save()

    // Log the update operation
    logger.info(`Updated ${this.model.name} record ID ${id}`, {
      recordId: id,
      modelName: this.model.name,
      updatedFields: Object.keys(updateData),
    })

    return record
  }

  /**
   * Delete a record
   */
  async delete(id: number): Promise<void> {
    const record = await this.findByIdOrFail(id)

    await record.delete()
  }

  /**
   * Check if record exists by ID
   */
  async exists(id: number): Promise<boolean> {
    const record = await this.findById(id)
    return record !== null
  }

  /**
   * Get record count with optional filters
   */
  async count(filters: Record<string, any> = {}): Promise<number> {
    const query = this.getQuery()

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query.where(key, value)
      }
    })

    return Number(await query.getCount())
  }

  /**
   * Get paginated records with filtering and search
   */
  async getPaginated(
    filters: BasePaginationFilters & Record<string, any> = {}
  ): Promise<BasePaginationResult<InstanceType<T>>> {
    const { page = 1, limit = 10, search, ...otherFilters } = filters

    const query = this.getQuery()

    // Apply search filter if search fields are defined
    if (search && this.searchFields.length > 0) {
      query.where((builder) => {
        this.searchFields.forEach((field, index) => {
          if (index === 0) {
            builder.where(field, 'like', `%${search}%`)
          } else {
            builder.orWhere(field, 'like', `%${search}%`)
          }
        })
      })
    }

    // Apply other filters
    Object.entries(otherFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query.where(key, value)
      }
    })

    // Apply default ordering
    query.orderBy('created_at', 'desc')

    // Apply pagination and get results
    const paginatedResults = await query.paginate(page, limit)

    return {
      data: paginatedResults.all() as InstanceType<T>[],
      pagination: {
        currentPage: paginatedResults.currentPage,
        perPage: paginatedResults.perPage,
        total: paginatedResults.total,
        lastPage: paginatedResults.lastPage,
        hasMorePages: paginatedResults.hasMorePages,
        hasPages: paginatedResults.hasPages,
      },
    }
  }

  /**
   * Find multiple records by IDs
   */
  async findByIds(ids: number[]): Promise<InstanceType<T>[]> {
    if (ids.length === 0) return []

    return (await this.getQuery().whereIn('id', ids)) as InstanceType<T>[]
  }

  /**
   * Find first record matching conditions
   */
  async findFirst(conditions: Record<string, any>): Promise<InstanceType<T> | null> {
    const query = this.getQuery()

    Object.entries(conditions).forEach(([key, value]) => {
      query.where(key, value)
    })

    return (await query.first()) as InstanceType<T> | null
  }

  /**
   * Find all records matching conditions
   */
  async findWhere(conditions: Record<string, any>): Promise<InstanceType<T>[]> {
    const query = this.getQuery()

    Object.entries(conditions).forEach(([key, value]) => {
      query.where(key, value)
    })

    return (await query) as InstanceType<T>[]
  }

  /**
   * Bulk create records
   */
  async bulkCreate(dataArray: Partial<InstanceType<T>>[]): Promise<InstanceType<T>[]> {
    const records: InstanceType<T>[] = []

    for (const data of dataArray) {
      const record = await this.create(data)
      records.push(record)
    }

    return records
  }

  /**
   * Bulk update records by IDs
   */
  async bulkUpdate(ids: number[], data: Partial<InstanceType<T>>): Promise<void> {
    const query = this.getQuery()

    await query.whereIn('id', ids).update(data)
  }

  /**
   * Bulk delete records by IDs
   */
  async bulkDelete(ids: number[]): Promise<void> {
    if (ids.length === 0) return

    const query = this.getQuery()
    await query.whereIn('id', ids).delete()
  }

  /**
   * Transform and sanitize update data using whitelist approach
   */
  protected transformUpdateData(data: any): Partial<InstanceType<T>> {
    const updateData: any = {}

    // Only allow whitelisted fields
    this.allowedUpdateFields.forEach((field) => {
      if (data[field] !== undefined) {
        console.log('transformUpdateData', field, data[field])
        updateData[field] = this.transformField(field, data[field])
      }
    })

    // Log attempt to update sensitive fields
    this.sensitiveFields.forEach((field) => {
      if (data[field] !== undefined) {
        logger.warn(`Attempt to update sensitive field '${field}' blocked`, {
          modelName: this.model.name,
          field,
          attemptedValue: this.sanitizeLogValue(data[field]),
        })
      }
    })

    return updateData
  }

  /**
   * Transform individual field values
   */
  protected transformField(field: string, value: any): any {
    // Handle date fields - convert Date to DateTime
    if (this.isDateField(field) && value instanceof Date) {
      return DateTime.fromJSDate(value)
    }

    // Handle JSON array fields
    if (this.isJsonArrayField(field) && Array.isArray(value)) {
      return JSON.stringify(this.sanitizeJsonArray(value))
    }

    // Handle JSON object fields
    if (this.isJsonObjectField(field) && typeof value === 'object' && value !== null) {
      return JSON.stringify(this.sanitizeJsonObject(value))
    }

    return value
  }

  /**
   * Sanitize values for logging (remove sensitive data)
   */
  protected sanitizeLogValue(value: any): string {
    if (typeof value === 'string' && value.length > 50) {
      return `${value.substring(0, 10)}...[${value.length} chars]`
    }
    if (typeof value === 'object') {
      return '[Object]'
    }
    return String(value)
  }

  /**
   * Sanitize JSON array to prevent injection
   */
  protected sanitizeJsonArray(arr: any[]): any[] {
    return arr.filter((item) => {
      // Only allow primitive types in arrays
      return typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean'
    })
  }

  /**
   * Sanitize JSON object to prevent injection
   */
  protected sanitizeJsonObject(obj: any): any {
    const sanitized: any = {}

    Object.keys(obj).forEach((key) => {
      // Only allow safe keys (alphanumeric + underscore)
      if (/^[a-zA-Z0-9_]+$/.test(key)) {
        const value = obj[key]
        // Only allow primitive types
        if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
          sanitized[key] = value
        }
      }
    })

    return sanitized
  }

  /**
   * Check if field is a date field (override in child classes)
   */
  protected isDateField(field: string): boolean {
    return field.includes('At') || field.includes('Date') || field.includes('Period')
  }

  /**
   * Check if field is a JSON array field (override in child classes)
   */
  protected isJsonArrayField(field: string): boolean {
    return field === 'features' || field === 'permissions' || field === 'tags'
  }

  /**
   * Check if field is a JSON object field (override in child classes)
   */
  protected isJsonObjectField(field: string): boolean {
    return field === 'metadata' || field === 'settings' || field === 'config'
  }
}
