import { inject } from '@adonisjs/core'
import UserPermission from '#models/user_permission'
import Permission from '#models/permission'
import BaseRepository, {
  BasePaginationFilters,
  BasePaginationResult,
} from '#repositories/base_repository'

export interface UserPermissionFilters extends BasePaginationFilters {
  userId?: number
  permissionId?: number
  tenantId?: number
}

/**
 * Repository for user permission database operations with security features
 */
@inject()
export default class UserPermissionRepository extends BaseRepository<typeof UserPermission> {
  protected model = UserPermission
  protected searchFields = []

  // Security: Define allowed fields for updates
  protected allowedUpdateFields = ['userId', 'permissionId', 'tenantId']

  // Security: Define sensitive fields that should never be updated
  protected sensitiveFields = ['id', 'created_at', 'updated_at']

  async getUserPermissions(
    filters: UserPermissionFilters = {}
  ): Promise<BasePaginationResult<UserPermission>> {
    const { page = 1, limit = 10, userId, permissionId, tenantId } = filters

    const query = this.getQuery().preload('user').preload('permission').preload('tenant')

    // Apply filters
    if (userId) {
      query.where('userId', userId)
    }

    if (permissionId) {
      query.where('permissionId', permissionId)
    }

    if (tenantId) {
      query.where('tenantId', tenantId)
    }

    const userPermissions = await query.paginate(page, limit)

    return {
      data: userPermissions.all(),
      pagination: {
        currentPage: userPermissions.currentPage,
        perPage: userPermissions.perPage,
        total: userPermissions.total,
        lastPage: userPermissions.lastPage,
        hasMorePages: userPermissions.hasMorePages,
        hasPages: userPermissions.hasPages,
      },
    }
  }

  /**
   * Find user permission by user, permission, and tenant
   */
  async findByUserPermissionTenant(
    userId: number,
    permissionId: number,
    tenantId?: number | null
  ): Promise<UserPermission | null> {
    const query = this.getQuery().where('userId', userId).where('permissionId', permissionId)

    if (tenantId === null) {
      query.whereNull('tenantId')
    } else if (tenantId !== undefined) {
      query.where('tenantId', tenantId)
    }

    return await query.first()
  }

  /**
   * Get all permissions for a user in a tenant
   */
  async getUserPermissionsInTenant(userId: number, tenantId: number): Promise<UserPermission[]> {
    return await this.getQuery()
      .where('userId', userId)
      .where('tenantId', tenantId)
      .preload('permission')
  }

  /**
   * Get all users with a specific permission in a tenant
   */
  async getUsersWithPermissionInTenant(
    permissionId: number,
    tenantId: number
  ): Promise<UserPermission[]> {
    return await this.getQuery()
      .where('permissionId', permissionId)
      .where('tenantId', tenantId)
      .preload('user')
  }

  /**
   * Check if user has permission in tenant
   */
  async hasPermission(userId: number, permissionId: number, tenantId: number): Promise<boolean> {
    const userPermission = await this.findByUserPermissionTenant(userId, permissionId, tenantId)
    return !!userPermission
  }

  /**
   * Assign permission to user in tenant
   */
  async assignPermission(
    userId: number,
    permissionId: number,
    tenantId?: number | null
  ): Promise<UserPermission> {
    // Check if already exists
    const existing = await this.findByUserPermissionTenant(userId, permissionId, tenantId)
    if (existing) {
      return existing
    }

    return await this.create({
      userId,
      permissionId,
      tenantId,
    })
  }

  /**
   * Remove all permissions for a user in a tenant
   */
  async removeAllUserPermissions(userId: number, tenantId?: number | null): Promise<void> {
    const query = this.getQuery().where('userId', userId)

    if (tenantId !== undefined) {
      if (tenantId === null) {
        query.whereNull('tenantId')
      } else {
        query.where('tenantId', tenantId)
      }
    }

    await query.delete()
  }

  /**
   * Get user direct permissions (not from roles)
   */
  async getUserDirectPermissions(userId: number, tenantId?: number | null): Promise<string[]> {
    const userPermissions = await this.getQuery()
      .where('userId', userId)
      .if(tenantId !== undefined, (query) => {
        if (tenantId === null) {
          query.whereNull('tenantId')
        } else if (tenantId !== undefined) {
          query.where('tenantId', tenantId!)
        }
      })
      .preload('permission')

    return userPermissions.map((up) => up.permission.name)
  }

  /**
   * Assign permission by name to user
   */
  async assignPermissionByName(
    userId: number,
    permissionName: string,
    tenantId?: number | null
  ): Promise<UserPermission | null> {
    // First find the permission by name
    const permission = await Permission.findBy('name', permissionName)

    if (!permission) {
      throw new Error(`Permission '${permissionName}' not found`)
    }

    return await this.assignPermission(userId, permission.id, tenantId)
  }

  /**
   * Remove permission from user in tenant
   */
  async removePermission(
    userId: number,
    permissionId: number,
    tenantId?: number | null
  ): Promise<boolean> {
    const userPermission = await this.findByUserPermissionTenant(userId, permissionId, tenantId)
    if (!userPermission) {
      return false
    }

    await this.delete(userPermission.id)
    return true
  }

  /**
   * Remove permission by name from user
   */
  async removePermissionByName(
    userId: number,
    permissionName: string,
    tenantId?: number | null
  ): Promise<boolean> {
    const permission = await Permission.findBy('name', permissionName)
    if (!permission) {
      return false
    }

    return await this.removePermission(userId, permission.id, tenantId)
  }

  /**
   * Remove all permissions for user in tenant
   */
  async removeAllUserPermissionsInTenant(userId: number, tenantId: number): Promise<number> {
    const userPermissions = await this.getUserPermissionsInTenant(userId, tenantId)

    for (const userPermission of userPermissions) {
      await this.delete(userPermission.id)
    }

    return userPermissions.length
  }

  /**
   * Bulk assign permissions to a user
   */
  async bulkAssignPermissions(
    userId: number,
    permissionAssignments: Array<{ permissionId: number; tenantId?: number | null }>
  ): Promise<UserPermission[]> {
    const results: UserPermission[] = []
    for (const assignment of permissionAssignments) {
      const userPermission = await this.assignPermission(
        userId,
        assignment.permissionId,
        assignment.tenantId
      )
      results.push(userPermission)
    }
    return results
  }

  /**
   * Get user direct permissions formatted for API responses
   */
  async getUserDirectPermissionsFormatted(userId: number, tenantId?: number | null) {
    const userPermissions = await this.getQuery()
      .where('userId', userId)
      .if(tenantId !== undefined, (query) => {
        if (tenantId === null) {
          query.whereNull('tenantId')
        } else if (tenantId !== undefined) {
          query.where('tenantId', tenantId)
        }
      })
      .preload('permission')

    return userPermissions.map((userPermission) => ({
      id: userPermission.permission.id,
      name: userPermission.permission.name,
      displayName: userPermission.permission.displayName,
      description: userPermission.permission.description,
      resource: userPermission.permission.resource,
      action: userPermission.permission.action,
      tenantId: userPermission.tenantId,
    }))
  }

  /**
   * Check if user has a specific direct permission (optionally within a tenant)
   */
  async hasDirectPermission(
    userId: number,
    permissionName: string,
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserDirectPermissions(userId, tenantId)
    return permissions.includes(permissionName)
  }

  /**
   * Check if user has any of the specified direct permissions
   */
  async hasAnyDirectPermission(
    userId: number,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserDirectPermissions(userId, tenantId)
    return permissionNames.some((name) => permissions.includes(name))
  }

  /**
   * Check if user has all of the specified direct permissions
   */
  async hasAllDirectPermissions(
    userId: number,
    permissionNames: string[],
    tenantId?: number | null
  ): Promise<boolean> {
    const permissions = await this.getUserDirectPermissions(userId, tenantId)
    return permissionNames.every((name) => permissions.includes(name))
  }
}
