import { inject } from '@adonisjs/core'
import Tenant, { TenantStatus } from '#models/tenant'
import BaseRepository, { BasePaginationFilters } from './base_repository.js'
import { DateTime } from 'luxon'

export interface CreateTenantData {
  name: string
  slug: string
  status?: TenantStatus
  trialEndsAt?: Date | DateTime | null
}

export interface UpdateTenantData {
  name?: string
  slug?: string
  status?: TenantStatus
  trialEndsAt?: Date | DateTime | null
}

export interface TenantFilters extends BasePaginationFilters {
  status?: TenantStatus
}

@inject()
export default class TenantRepository extends BaseRepository<typeof Tenant> {
  protected model = Tenant
  protected searchFields = ['name', 'slug']

  /**
   * Find tenant by slug
   */
  async findBySlug(slug: string): Promise<Tenant | null> {
    return await this.findBy('slug', slug)
  }

  /**
   * Find tenant by slug or throw error
   */
  async findBySlugOrFail(slug: string): Promise<Tenant> {
    return await this.findByOrFail('slug', slug)
  }

  /**
   * Create a new tenant with validation and tenant-specific logic
   */
  async createTenant(data: CreateTenantData): Promise<Tenant> {
    // Convert DateTime to Date if needed
    let trialEndsAt: DateTime | null = null
    if (data.trialEndsAt) {
      trialEndsAt =
        data.trialEndsAt instanceof Date ? DateTime.fromJSDate(data.trialEndsAt) : data.trialEndsAt
    }

    const tenantData = {
      name: data.name,
      slug: data.slug,
      status: data.status || TenantStatus.TRIAL,
      trialEndsAt,
    }

    return await super.create(tenantData)
  }

  /**
   * Update an existing tenant
   */
  async update(id: number, data: UpdateTenantData): Promise<Tenant> {
    // Convert DateTime to Date if needed
    const updateData: Partial<Tenant> = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.slug !== undefined) updateData.slug = data.slug
    if (data.status !== undefined) updateData.status = data.status
    if (data.trialEndsAt !== undefined) {
      updateData.trialEndsAt = data.trialEndsAt
        ? data.trialEndsAt instanceof Date
          ? DateTime.fromJSDate(data.trialEndsAt)
          : data.trialEndsAt
        : null
    }

    return await super.update(id, updateData)
  }

  /**
   * Get tenants with pagination and filtering
   */
  async getTenants(filters: TenantFilters = {}) {
    // Use base getPaginated method which handles pagination, search, and basic filtering
    return await this.getPaginated(filters)
  }

  /**
   * Find tenants by status
   */
  async findByStatus(status: TenantStatus): Promise<Tenant[]> {
    return await this.findWhere({ status })
  }

  /**
   * Find active tenants
   */
  async findActive(): Promise<Tenant[]> {
    return await this.findByStatus(TenantStatus.ACTIVE)
  }

  /**
   * Find trial tenants
   */
  async findTrial(): Promise<Tenant[]> {
    return await this.findByStatus(TenantStatus.TRIAL)
  }

  /**
   * Check if slug exists
   */
  async slugExists(slug: string): Promise<boolean> {
    const tenant = await this.findBySlug(slug)
    return tenant !== null
  }

  /**
   * Get tenant count by status
   */
  async countByStatus(status: TenantStatus): Promise<number> {
    return await this.count({ status })
  }
}
