import { inject } from '@adonisjs/core'
import Role from '#models/role'
import BaseRepository, {
  BasePaginationFilters,
  BasePaginationResult,
} from '#repositories/base_repository'

export interface RoleFilters extends BasePaginationFilters {
  search?: string
}

/**
 * Repository for role database operations with security features
 */
@inject()
export default class RoleRepository extends BaseRepository<typeof Role> {
  protected model = Role
  protected searchFields = ['name', 'displayName']

  // Security: Define allowed fields for updates
  protected allowedUpdateFields = ['name', 'displayName', 'description']

  // Security: Define sensitive fields that should never be updated
  protected sensitiveFields = ['id', 'created_at', 'updated_at']

  /**
   * Find role by name
   */
  async findByName(name: string): Promise<Role | null> {
    return await this.findBy('name', name)
  }

  /**
   * Find role by name or throw error
   */
  async findByNameOrFail(name: string): Promise<Role> {
    return await this.findByOrFail('name', name)
  }

  /**
   * Get role with permissions
   */
  async findWithPermissions(id: number): Promise<Role | null> {
    return await this.getQuery().where('id', id).preload('permissions').first()
  }

  /**
   * Check if role exists by name
   */
  async existsByName(name: string): Promise<boolean> {
    const role = await this.findByName(name)
    return !!role
  }
}
