import { BaseSeeder } from '@adonisjs/lucid/seeders'
import Role from '#models/role'
import Permission from '#models/permission'

export default class extends BaseSeeder {
  static environment = ['development', 'testing', 'production']

  async run() {
    // Create permissions first
    const permissions = await this.createPermissions()

    // Create roles
    const roles = await this.createRoles()

    // Assign permissions to roles
    await this.assignPermissionsToRoles(roles, permissions)
  }

  private async createPermissions() {
    const permissionData = [
      // User management permissions
      {
        name: 'users.create',
        displayName: 'Create Users',
        description: 'Create new users',
        resource: 'users',
        action: 'create',
      },
      {
        name: 'users.read',
        displayName: 'Read Users',
        description: 'View user information',
        resource: 'users',
        action: 'read',
      },
      {
        name: 'users.update',
        displayName: 'Update Users',
        description: 'Update user information',
        resource: 'users',
        action: 'update',
      },
      {
        name: 'users.delete',
        displayName: 'Delete Users',
        description: 'Delete users',
        resource: 'users',
        action: 'delete',
      },
      {
        name: 'users.manage',
        displayName: 'Manage Users',
        description: 'Full user management access',
        resource: 'users',
        action: 'manage',
      },

      // Role and permission management
      {
        name: 'roles.create',
        displayName: 'Create Roles',
        description: 'Create new roles',
        resource: 'roles',
        action: 'create',
      },
      {
        name: 'roles.read',
        displayName: 'Read Roles',
        description: 'View role information',
        resource: 'roles',
        action: 'read',
      },
      {
        name: 'roles.update',
        displayName: 'Update Roles',
        description: 'Update role information',
        resource: 'roles',
        action: 'update',
      },
      {
        name: 'roles.delete',
        displayName: 'Delete Roles',
        description: 'Delete roles',
        resource: 'roles',
        action: 'delete',
      },
      {
        name: 'roles.manage',
        displayName: 'Manage Roles',
        description: 'Full role management access',
        resource: 'roles',
        action: 'manage',
      },

      {
        name: 'permissions.read',
        displayName: 'Read Permissions',
        description: 'View permission information',
        resource: 'permissions',
        action: 'read',
      },
      {
        name: 'permissions.manage',
        displayName: 'Manage Permissions',
        description: 'Full permission management access',
        resource: 'permissions',
        action: 'manage',
      },

      // Analytics permissions
      {
        name: 'analytics.read',
        displayName: 'Read Analytics',
        description: 'View analytics data',
        resource: 'analytics',
        action: 'read',
      },

      // Billing permissions
      {
        name: 'billing.create',
        displayName: 'Create Billing',
        description: 'Create billing records',
        resource: 'billing',
        action: 'create',
      },
      {
        name: 'billing.read',
        displayName: 'View Billing',
        description: 'View billing information',
        resource: 'billing',
        action: 'read',
      },
      {
        name: 'billing.update',
        displayName: 'Update Billing',
        description: 'Update billing records',
        resource: 'billing',
        action: 'update',
      },
      {
        name: 'billing.delete',
        displayName: 'Delete Billing',
        description: 'Delete billing records',
        resource: 'billing',
        action: 'delete',
      },
      {
        name: 'billing.manage',
        displayName: 'Manage Billing',
        description: 'Full billing management access',
        resource: 'billing',
        action: 'manage',
      },
      {
        name: 'analytics.manage',
        displayName: 'Manage Analytics',
        description: 'Full analytics access',
        resource: 'analytics',
        action: 'manage',
      },

      // System administration
      {
        name: 'system.manage',
        displayName: 'System Management',
        description: 'Full system administration access',
        resource: 'system',
        action: 'manage',
      },
    ]

    const permissions: Record<string, any> = {}

    for (const permData of permissionData) {
      const permission = await Permission.updateOrCreate(
        { name: permData.name },
        { ...permData, isSystem: true }
      )
      permissions[permData.name] = permission
    }

    return permissions
  }

  private async createRoles() {
    const roleData = [
      {
        name: 'super_admin',
        displayName: 'Super Administrator',
        description: 'Full system access across all tenants',
        isSystem: true,
      },
      {
        name: 'tenant_owner',
        displayName: 'Tenant Owner',
        description: 'Full access within their tenant',
        isSystem: true,
      },
      {
        name: 'tenant_admin',
        displayName: 'Tenant Administrator',
        description: 'Administrative access within their tenant',
        isSystem: true,
      },
      {
        name: 'merchant_manager',
        displayName: 'Merchant Manager',
        description: 'Manage merchants within their tenant',
        isSystem: true,
      },
      {
        name: 'merchant_staff',
        displayName: 'Merchant Staff',
        description: 'Basic merchant access',
        isSystem: true,
      },
      {
        name: 'viewer',
        displayName: 'Viewer',
        description: 'Read-only access',
        isSystem: true,
      },
    ]

    const roles: Record<string, any> = {}

    for (const roleInfo of roleData) {
      const role = await Role.updateOrCreate({ name: roleInfo.name }, roleInfo)
      roles[roleInfo.name] = role
    }

    return roles
  }

  private async assignPermissionsToRoles(
    roles: Record<string, any>,
    permissions: Record<string, any>
  ) {
    // Super Admin - All permissions
    await roles.super_admin
      .related('permissions')
      .sync(Object.values(permissions).map((p: any) => p.id))

    // Tenant Owner - All permissions except system management and tenant management
    const tenantOwnerPermissions = Object.values(permissions)
      .filter((p: any) => !p.name.startsWith('system.') && !p.name.startsWith('tenants.'))
      .map((p: any) => p.id)
    await roles.tenant_owner.related('permissions').sync(tenantOwnerPermissions)

    // Tenant Admin - User management within tenant
    const tenantAdminPermissions = [
      permissions['users.read'].id,
      permissions['users.update'].id,
      permissions['analytics.read'].id,
      permissions['billing.read'].id,
    ]
    await roles.tenant_admin.related('permissions').sync(tenantAdminPermissions)

    // Merchant Manager - Analytics access
    const merchantManagerPermissions = [permissions['analytics.read'].id]
    await roles.merchant_manager.related('permissions').sync(merchantManagerPermissions)

    // Merchant Staff - Basic access
    const merchantStaffPermissions = [permissions['users.read'].id]
    await roles.merchant_staff.related('permissions').sync(merchantStaffPermissions)

    // Viewer - Read-only access
    const viewerPermissions = [
      permissions['users.read'].id,
      permissions['analytics.read'].id,
      permissions['billing.read'].id,
    ]
    await roles.viewer.related('permissions').sync(viewerPermissions)
  }
}
