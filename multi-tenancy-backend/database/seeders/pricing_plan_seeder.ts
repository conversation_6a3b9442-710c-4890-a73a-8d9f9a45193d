import { BaseSeeder } from '@adonisjs/lucid/seeders'
import PricingPlan from '#models/pricing_plan'
import { BillingPlan, BillingCycle } from '#models/billing'
import Stripe from 'stripe'
import env from '#start/env'

export default class PricingPlanSeeder extends BaseSeeder {
  static environment = ['development', 'testing', 'production']

  private stripe: Stripe | null = null

  async run() {
    console.log('🔄 Creating pricing plans...')

    // Initialize Stripe if available
    await this.initializeStripe()

    // Define pricing plans data
    const pricingPlansData = [
      // FREE Plan
      {
        name: 'Free',
        slug: BillingPlan.FREE,
        description: 'Perfect for getting started with basic loyalty features',
        monthlyPrice: 0, // Price in cents
        yearlyPrice: 0,
        currency: 'usd',
        isPopular: false,
        isActive: true,
        sortOrder: 1,
        stripePriceIdMonthly: null,
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.FREE)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.FREE)),
        trialDays: 14,
      },
      // BASIC Plan
      {
        name: 'Basic',
        slug: BillingPlan.BASIC,
        description: 'Great for small businesses with growing customer base',
        monthlyPrice: 2900, // $29.00 in cents
        yearlyPrice: 29000, // $290.00 in cents (10 months price)
        currency: 'usd',
        isPopular: false,
        isActive: true,
        sortOrder: 2,
        stripePriceIdMonthly: null, // Will be set after Stripe integration
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.BASIC)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.BASIC)),
        trialDays: 14,
      },
      // PRO Plan
      {
        name: 'Pro',
        slug: BillingPlan.PRO,
        description: 'Perfect for growing businesses with advanced needs',
        monthlyPrice: 9900, // $99.00 in cents
        yearlyPrice: 99000, // $990.00 in cents (10 months price)
        currency: 'usd',
        isPopular: true,
        isActive: true,
        sortOrder: 3,
        stripePriceIdMonthly: null, // Will be set after Stripe integration
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.PRO)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.PRO)),
        trialDays: 14,
      },
      // ENTERPRISE Plan
      {
        name: 'Enterprise',
        slug: BillingPlan.ENTERPRISE,
        description: 'For large businesses with custom requirements',
        monthlyPrice: 29900, // $299.00 in cents
        yearlyPrice: 299000, // $2990.00 in cents (10 months price)
        currency: 'usd',
        isPopular: false,
        isActive: true,
        sortOrder: 4,
        stripePriceIdMonthly: null, // Will be set after Stripe integration
        stripePriceIdYearly: null,
        features: JSON.stringify(this.getDefaultFeatures(BillingPlan.ENTERPRISE)),
        limits: JSON.stringify(this.getDefaultLimits(BillingPlan.ENTERPRISE)),
        trialDays: 14,
      },
    ]

    // Create or update pricing plans with Stripe integration
    for (const planData of pricingPlansData) {
      console.log(`🔄 Processing plan: ${planData.name}`)

      // Create or get Stripe product and prices if Stripe is available
      if (this.stripe) {
        await this.createOrGetStripeData(planData)
      }

      const existingPlan = await PricingPlan.query().where('slug', planData.slug).first()

      if (!existingPlan) {
        await PricingPlan.create(planData)
        console.log(`✅ Created pricing plan: ${planData.slug}`)
      } else {
        // Update existing plan with new data
        await existingPlan.merge(planData).save()
        console.log(`✅ Updated pricing plan: ${planData.slug}`)
      }
    }

    console.log('✅ Pricing plans seeding completed!')
  }

  private async initializeStripe(): Promise<void> {
    try {
      const stripeSecretKey = env.get('STRIPE_SECRET_KEY')
      if (!stripeSecretKey) {
        console.log('⚠️  STRIPE_SECRET_KEY not found - skipping Stripe integration')
        return
      }

      this.stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2025-02-24.acacia',
      })

      console.log(`🔑 Stripe initialized with key: ${stripeSecretKey.substring(0, 12)}...`)
    } catch (error: any) {
      console.log(`⚠️  Failed to initialize Stripe: ${error.message}`)
      this.stripe = null
    }
  }

  private async createOrGetStripeData(planData: any): Promise<void> {
    if (!this.stripe) return

    try {
      // Create or get Stripe product
      const product = await this.createOrGetStripeProduct(planData)

      // Create or get monthly price if plan has monthly pricing
      if (planData.monthlyPrice > 0) {
        const monthlyPrice = await this.createOrGetStripePrice(
          product.id,
          planData.monthlyPrice,
          'month',
          `${planData.name} Monthly`
        )
        planData.stripePriceIdMonthly = monthlyPrice.id
      }

      // Create or get yearly price if plan has yearly pricing
      if (planData.yearlyPrice > 0) {
        const yearlyPrice = await this.createOrGetStripePrice(
          product.id,
          planData.yearlyPrice,
          'year',
          `${planData.name} Yearly`
        )
        planData.stripePriceIdYearly = yearlyPrice.id
      }

      console.log(`   ✅ Stripe data ready for ${planData.name}`)
    } catch (error: any) {
      console.log(`   ⚠️  Stripe integration failed for ${planData.name}: ${error.message}`)
    }
  }

  private async createOrGetStripeProduct(planData: any): Promise<Stripe.Product> {
    if (!this.stripe) throw new Error('Stripe not initialized')

    try {
      // First, try to find existing product by name
      const existingProducts = await this.stripe.products.list({
        limit: 100,
      })

      const existingProduct = existingProducts.data.find(
        (product) => product.name === planData.name && product.active
      )

      if (existingProduct) {
        console.log(
          `   🔍 Found existing Stripe product: ${existingProduct.id} (${existingProduct.name})`
        )
        return existingProduct
      }

      // Create new product if not found
      const product = await this.stripe.products.create({
        name: planData.name,
        description: planData.description,
        metadata: {
          plan_slug: planData.slug,
          created_by: 'pricing_plan_seeder',
        },
      })

      console.log(`   ✅ Created new Stripe product: ${product.id} (${product.name})`)
      return product
    } catch (error: any) {
      throw new Error(`Failed to create/get Stripe product: ${error.message}`)
    }
  }

  private async createOrGetStripePrice(
    productId: string,
    unitAmount: number,
    interval: 'month' | 'year',
    nickname: string
  ): Promise<Stripe.Price> {
    if (!this.stripe) throw new Error('Stripe not initialized')

    try {
      // First, try to find existing price
      const existingPrices = await this.stripe.prices.list({
        product: productId,
        limit: 100,
      })

      const existingPrice = existingPrices.data.find(
        (price) =>
          price.unit_amount === unitAmount && price.recurring?.interval === interval && price.active
      )

      if (existingPrice) {
        console.log(`   🔍 Found existing Stripe price: ${existingPrice.id} (${nickname})`)
        return existingPrice
      }

      // Create new price if not found
      const price = await this.stripe.prices.create({
        product: productId,
        unit_amount: unitAmount,
        currency: 'usd',
        recurring: {
          interval: interval,
        },
        nickname: nickname,
        metadata: {
          created_by: 'pricing_plan_seeder',
        },
      })

      console.log(`   ✅ Created new Stripe price: ${price.id} (${nickname})`)
      return price
    } catch (error: any) {
      throw new Error(`Failed to create/get Stripe price: ${error.message}`)
    }
  }

  private getDefaultLimits(plan: BillingPlan) {
    const limits = {
      [BillingPlan.FREE]: {
        maxCustomers: 100,
        maxLocations: 1,
        maxCampaigns: 2,
        maxRewards: 5,
      },
      [BillingPlan.BASIC]: {
        maxCustomers: 1000,
        maxLocations: 3,
        maxCampaigns: 10,
        maxRewards: 20,
      },
      [BillingPlan.PRO]: {
        maxCustomers: 10000,
        maxLocations: 10,
        maxCampaigns: 50,
        maxRewards: 100,
      },
      [BillingPlan.ENTERPRISE]: {
        maxCustomers: null, // Unlimited
        maxLocations: null, // Unlimited
        maxCampaigns: null, // Unlimited
        maxRewards: null, // Unlimited
      },
    }

    return limits[plan]
  }

  private getDefaultFeatures(plan: BillingPlan) {
    const features = {
      [BillingPlan.FREE]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Basic customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: false },
        { name: 'Marketing Campaigns', description: 'Automated marketing tools', included: false },
        { name: 'Multi-Location', description: 'Multiple store locations', included: false },
        { name: 'API Access', description: 'REST API integration', included: false },
      ],
      [BillingPlan.BASIC]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Advanced customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: true },
        {
          name: 'Marketing Campaigns',
          description: 'Automated marketing tools',
          included: true,
          limit: 10,
        },
        {
          name: 'Multi-Location',
          description: 'Multiple store locations',
          included: true,
          limit: 3,
        },
        { name: 'API Access', description: 'REST API integration', included: false },
      ],
      [BillingPlan.PRO]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Advanced customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: true },
        {
          name: 'Marketing Campaigns',
          description: 'Automated marketing tools',
          included: true,
          limit: 50,
        },
        {
          name: 'Multi-Location',
          description: 'Multiple store locations',
          included: true,
          limit: 10,
        },
        { name: 'API Access', description: 'REST API integration', included: true },
        {
          name: 'Advanced Analytics',
          description: 'Detailed reports and insights',
          included: true,
        },
        { name: 'Custom Branding', description: 'White-label customization', included: true },
        {
          name: 'Priority Support',
          description: 'Priority email and chat support',
          included: true,
        },
      ],
      [BillingPlan.ENTERPRISE]: [
        {
          name: 'Basic Loyalty Program',
          description: 'Simple points-based rewards',
          included: true,
        },
        { name: 'Customer Management', description: 'Advanced customer profiles', included: true },
        { name: 'Mobile App Access', description: 'Customer mobile app', included: true },
        { name: 'Basic Analytics', description: 'Simple reports and insights', included: true },
        { name: 'Email Support', description: 'Standard email support', included: true },
        { name: 'Advanced Rewards', description: 'Tier-based rewards system', included: true },
        {
          name: 'Marketing Campaigns',
          description: 'Automated marketing tools',
          included: true,
          limit: null,
        },
        {
          name: 'Multi-Location',
          description: 'Multiple store locations',
          included: true,
          limit: null,
        },
        { name: 'API Access', description: 'REST API integration', included: true },
        {
          name: 'Advanced Analytics',
          description: 'Detailed reports and insights',
          included: true,
        },
        { name: 'Custom Branding', description: 'White-label customization', included: true },
        {
          name: 'Priority Support',
          description: 'Priority email and chat support',
          included: true,
        },
        { name: 'Advanced Security', description: 'Enhanced security features', included: true },
        { name: 'Custom Integrations', description: 'Custom API integrations', included: true },
        { name: 'Data Export', description: 'Full data export capabilities', included: true },
        { name: 'Dedicated Support', description: 'Dedicated account manager', included: true },
      ],
    }

    return features[plan]
  }
}
