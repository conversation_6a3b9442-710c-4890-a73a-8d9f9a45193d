import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_permissions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').notNullable()
      table
        .integer('user_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
      table
        .integer('permission_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('permissions')
        .onDelete('CASCADE')
      table
        .integer('tenant_id')
        .unsigned()
        .nullable() // null for global permissions
        .references('id')
        .inTable('tenants')
        .onDelete('CASCADE')
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Unique constraint to prevent duplicate user-permission assignments per tenant
      table.unique(['user_id', 'permission_id', 'tenant_id'])

      // Indexes
      table.index(['user_id'])
      table.index(['permission_id'])
      table.index(['tenant_id'])
      table.index(['user_id', 'tenant_id'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
