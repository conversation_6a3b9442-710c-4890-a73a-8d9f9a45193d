import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'permissions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').notNullable()
      table.string('name').notNullable().unique() // e.g., 'merchants.create', 'users.read'
      table.string('display_name').notNullable()
      table.text('description').nullable()
      table.string('resource').notNullable() // e.g., 'merchants', 'users', 'tenants'
      table.string('action').notNullable() // e.g., 'create', 'read', 'update', 'delete', 'manage'
      table.boolean('is_system').defaultTo(false) // System permissions cannot be deleted
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['name'])
      table.index(['resource'])
      table.index(['action'])
      table.index(['resource', 'action'])
      table.index(['is_system'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
