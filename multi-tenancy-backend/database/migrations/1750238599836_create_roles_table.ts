import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'roles'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').notNullable()
      table.string('name').notNullable().unique()
      table.string('display_name').notNullable()
      table.text('description').nullable()
      table.boolean('is_system').defaultTo(false) // System roles cannot be deleted
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['name'])
      table.index(['is_system'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
