import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'pricing_plans'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      // Basic plan information
      table.string('name', 100).notNullable()
      table.enum('slug', ['free', 'basic', 'pro', 'enterprise']).notNullable().unique()
      table.text('description').notNullable()

      // Pricing
      table.integer('monthly_price').notNullable().defaultTo(0) // Price in cents
      table.integer('yearly_price').notNullable().defaultTo(0) // Price in cents
      table.string('currency', 3).notNullable().defaultTo('usd')

      // Plan metadata
      table.boolean('is_popular').notNullable().defaultTo(false)
      table.boolean('is_active').notNullable().defaultTo(true)
      table.integer('sort_order').notNullable().defaultTo(0)

      // Stripe integration
      table.string('stripe_price_id_monthly').nullable()
      table.string('stripe_price_id_yearly').nullable()

      // Features and limits (JSON fields)
      table.text('features').notNullable() // JSON array of PlanFeature objects
      table.text('limits').notNullable() // JSON object of PlanLimits

      // Trial period
      table.integer('trial_days').notNullable().defaultTo(0)

      table.timestamp('created_at')
      table.timestamp('updated_at')

      // Indexes
      table.index(['slug'])
      table.index(['is_active'])
      table.index(['sort_order'])
      table.index(['is_popular'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
