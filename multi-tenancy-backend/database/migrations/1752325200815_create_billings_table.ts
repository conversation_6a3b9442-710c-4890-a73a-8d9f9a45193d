import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'billings'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      // Foreign key to tenants table
      table.integer('tenant_id').unsigned().notNullable()
      table.foreign('tenant_id').references('id').inTable('tenants').onDelete('CASCADE')
      table.unique('tenant_id') // Ensure 1-to-1 relationship

      // Plan and billing details
      table.enum('plan', ['free', 'basic', 'pro', 'enterprise']).notNullable().defaultTo('free')
      table
        .enum('status', ['active', 'suspended', 'cancelled', 'past_due'])
        .notNullable()
        .defaultTo('active')
      table.enum('cycle', ['monthly', 'yearly']).notNullable().defaultTo('monthly')

      // Pricing
      table.integer('amount').notNullable().defaultTo(0) // Amount in cents
      table.string('currency', 3).notNullable().defaultTo('usd')

      // Stripe integration
      table.string('stripe_customer_id').nullable()
      table.string('stripe_subscription_id').nullable()
      table.string('stripe_price_id').nullable()

      // Billing periods
      table.timestamp('current_period_start').nullable()
      table.timestamp('current_period_end').nullable()

      // Trial information
      table.timestamp('trial_start').nullable()
      table.timestamp('trial_end').nullable()

      // Cancellation
      table.timestamp('cancelled_at').nullable()
      table.boolean('cancel_at_period_end').notNullable().defaultTo(false)

      // Plan limits
      table.integer('max_users').nullable() // null means unlimited
      table.integer('max_storage').nullable() // Storage in GB, null means unlimited

      // Features (JSON array of enabled features)
      table.text('features').nullable()

      // Computed field for upgrade requirements
      table.boolean('needs_upgrade').defaultTo(false).notNullable()

      table.timestamp('created_at')
      table.timestamp('updated_at')

      // Indexes
      table.index(['status'])
      table.index(['plan'])
      table.index(['stripe_customer_id'])
      table.index(['stripe_subscription_id'])
      table.index(['current_period_end'])
      table.index(['trial_end'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
