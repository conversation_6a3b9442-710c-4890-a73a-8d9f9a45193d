{"name": "dd", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build --ignore-ts-errors", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "setup-stripe-pricing": "node ace run scripts/setup-stripe-pricing.ts"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#mails/*": "./app/mails/*.js", "#models/*": "./app/models/*.js", "#repositories/*": "./app/repositories/*.js", "#services/*": "./app/services/*.js", "#middleware/*": "./app/middleware/*.js", "#enums/*": "./app/enums/*.js", "#validators/*": "./app/validators/*.js", "#decorators/*": "./app/decorators/*.js", "#utils/*": "./app/utils/*.js", "#constants/*": "./app/constants/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/api-client": "^3.1.0", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.2.0", "@swc/core": "1.11.24", "@types/luxon": "^3.6.2", "@types/node": "^22.15.18", "eslint": "^9.26.0", "hot-hook": "^0.4.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "ts-node-maintained": "^10.9.5", "typescript": "~5.8"}, "dependencies": {"@adonisjs/ally": "^5.1.0", "@adonisjs/auth": "^9.4.0", "@adonisjs/bouncer": "^3.1.6", "@adonisjs/core": "^6.19.0", "@adonisjs/cors": "^2.2.1", "@adonisjs/lucid": "^21.6.1", "@adonisjs/mail": "^9.2.2", "@adonisjs/view": "^7.0.0-7", "@vinejs/vine": "^3.0.1", "edge.js": "^6.3.0", "luxon": "^3.6.1", "mjml": "^4.15.3", "mysql2": "^3.14.1", "reflect-metadata": "^0.2.2", "stripe": "^17.7.0"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "prettier": "@adonisjs/prettier-config"}