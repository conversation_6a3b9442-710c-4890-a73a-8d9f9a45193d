erDiagram
%% Core User Management
USERS {
int id PK
string email UK
string password
string full_name
enum status "active, inactive, suspended"
timestamp email_verified_at
timestamp created_at
timestamp updated_at
}

    %% Role-Based Access Control
    ROLES {
        int id PK
        string name UK "super_admin, merchant_admin, merchant_user, store_manager, etc"
        string display_name
        string description
        timestamp created_at
        timestamp updated_at
    }

    PERMISSIONS {
        int id PK
        string name UK "users.create, stores.manage, products.edit, etc"
        string display_name
        string description
        string resource "users, stores, products, orders, etc"
        string action "create, read, update, delete, manage"
        timestamp created_at
        timestamp updated_at
    }

    ROLE_PERMISSIONS {
        int id PK
        int role_id FK
        int permission_id FK
        timestamp created_at
    }

    USER_ROLES {
        int id PK
        int user_id FK
        int role_id FK
        int tenant_id FK "null for super admin"
        timestamp created_at
        timestamp updated_at
    }

    %% Multi-Tenancy Core
    TENANTS {
        int id PK
        string name
        string slug UK "unique identifier for subdomain"
        enum status "active, inactive, suspended, trial"
        json settings "tenant-specific configurations"
        timestamp trial_ends_at
        timestamp created_at
        timestamp updated_at
    }

    %% Store/Domain Management
    STORES {
        int id PK
        int tenant_id FK
        string name
        string description
        enum status "active, inactive, maintenance"
        json settings "store-specific settings"
        timestamp created_at
        timestamp updated_at
    }

    STORE_DOMAINS {
        int id PK
        int store_id FK
        string domain UK "subdomain.platform.com or custom.com"
        enum type "subdomain, custom"
        enum status "active, pending, verified, failed"
        boolean is_primary
        json ssl_config "SSL certificate info"
        timestamp verified_at
        timestamp created_at
        timestamp updated_at
    }

    %% Subscription Management
    SUBSCRIPTION_PLANS {
        int id PK
        string name
        string description
        decimal price
        enum billing_cycle "monthly, yearly"
        json features "feature limits and permissions"
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    TENANT_SUBSCRIPTIONS {
        int id PK
        int tenant_id FK
        int plan_id FK
        enum status "active, cancelled, expired, trial"
        timestamp starts_at
        timestamp ends_at
        timestamp cancelled_at
        decimal amount
        timestamp created_at
        timestamp updated_at
    }

    %% Product Management (Example business logic)
    CATEGORIES {
        int id PK
        int store_id FK
        string name
        string description
        string slug
        int parent_id FK "self-reference for nested categories"
        boolean is_active
        int sort_order
        timestamp created_at
        timestamp updated_at
    }

    PRODUCTS {
        int id PK
        int store_id FK
        int category_id FK
        string name
        string description
        string sku UK "per store"
        decimal price
        int stock_quantity
        enum status "active, inactive, out_of_stock"
        json attributes "flexible product attributes"
        timestamp created_at
        timestamp updated_at
    }

    %% Order Management
    CUSTOMERS {
        int id PK
        int store_id FK
        string email
        string first_name
        string last_name
        string phone
        json address
        timestamp created_at
        timestamp updated_at
    }

    ORDERS {
        int id PK
        int store_id FK
        int customer_id FK
        string order_number UK "per store"
        enum status "pending, processing, shipped, delivered, cancelled"
        decimal subtotal
        decimal tax_amount
        decimal shipping_amount
        decimal total_amount
        json shipping_address
        json billing_address
        timestamp created_at
        timestamp updated_at
    }

    ORDER_ITEMS {
        int id PK
        int order_id FK
        int product_id FK
        string product_name "snapshot"
        decimal unit_price "snapshot"
        int quantity
        decimal total_price
        timestamp created_at
    }

    %% Audit Trail
    ACTIVITY_LOGS {
        int id PK
        int user_id FK
        int tenant_id FK
        string action
        string resource_type
        int resource_id
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }

    %% Relationships
    USERS ||--o{ USER_ROLES : "has"
    ROLES ||--o{ ROLE_PERMISSIONS : "has"
    PERMISSIONS ||--o{ ROLE_PERMISSIONS : "belongs to"
    ROLES ||--o{ USER_ROLES : "assigned to"

    TENANTS ||--o{ USER_ROLES : "scoped to"
    TENANTS ||--o{ STORES : "owns"
    TENANTS ||--o{ TENANT_SUBSCRIPTIONS : "has"
    TENANTS ||--o{ ACTIVITY_LOGS : "tracks"

    STORES ||--o{ STORE_DOMAINS : "has"
    STORES ||--o{ CATEGORIES : "contains"
    STORES ||--o{ PRODUCTS : "sells"
    STORES ||--o{ CUSTOMERS : "serves"
    STORES ||--o{ ORDERS : "processes"

    SUBSCRIPTION_PLANS ||--o{ TENANT_SUBSCRIPTIONS : "subscribed to"

    CATEGORIES ||--o{ PRODUCTS : "categorizes"
    CATEGORIES ||--o{ CATEGORIES : "parent-child"

    CUSTOMERS ||--o{ ORDERS : "places"
    ORDERS ||--o{ ORDER_ITEMS : "contains"
    PRODUCTS ||--o{ ORDER_ITEMS : "ordered as"

    USERS ||--o{ ACTIVITY_LOGS : "performs"
