@mjml()
  <mjml>
    <mj-head>
      <mj-title>Welcome to {{ tenantName }}</mj-title>
      <mj-preview>You've been invited to join {{ tenantName }}. Get started with your temporary password.</mj-preview>
      <mj-attributes>
        <mj-all font-family="'Helvetica Neue', Helvetica, Arial, sans-serif"></mj-all>
        <mj-text font-weight="400" font-size="16px" color="#000000" line-height="24px" font-family="'Helvetica Neue', Helvetica, Arial, sans-serif"></mj-text>
      </mj-attributes>
      <mj-style inline="inline">
        .blue-link a {
          color: #2563eb !important;
          text-decoration: none !important;
        }
        .blue-link a:hover {
          text-decoration: underline !important;
        }
        .password-box {
          background-color: #f8fafc !important;
          border: 2px dashed #cbd5e1 !important;
          border-radius: 8px !important;
          padding: 16px !important;
          font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace !important;
          font-size: 18px !important;
          font-weight: bold !important;
          color: #1e293b !important;
          text-align: center !important;
          letter-spacing: 2px !important;
        }
      </mj-style>
    </mj-head>
    <mj-body background-color="#f1f5f9">
      @include('emails/components/header')

      <!-- Main Content Section -->
      <mj-section background-color="#ffffff" padding="0 40px">
        <mj-column>
          <mj-spacer height="40px" />

          <!-- Welcome Message -->
          <mj-text font-size="24px" font-weight="600" color="#1e293b" line-height="32px">
            Welcome! You've been invited to join {{ tenantName }}
          </mj-text>

          <mj-spacer height="24px" />

          <mj-text font-size="16px" color="#475569" line-height="24px">
            Great news! You've been invited to join <strong>{{ tenantName }}</strong>. We're excited to have you on board and can't wait for you to get started.
          </mj-text>

          <mj-spacer height="32px" />

          <!-- Temporary Password Section -->
          <mj-text font-size="18px" font-weight="600" color="#1e293b">
            Your Temporary Password
          </mj-text>

          <mj-spacer height="16px" />

          <mj-text font-size="14px" color="#64748b" line-height="20px">
            Use this temporary password to log in for the first time. You'll be prompted to change it after your first login.
          </mj-text>

          <mj-spacer height="16px" />

          <mj-raw>
            <div class="password-box">
              {{ temporaryPassword }}
            </div>
          </mj-raw>

          <mj-spacer height="32px" />

          <!-- Call to Action Button -->
          <mj-button
            background-color="#2563eb"
            color="#ffffff"
            font-size="16px"
            font-weight="600"
            border-radius="8px"
            padding="16px 32px"
            href="#"
          >
            Get Started
          </mj-button>

          <mj-spacer height="32px" />

          <!-- Instructions -->
          <mj-text font-size="16px" color="#475569" line-height="24px">
            <strong>Next steps:</strong>
          </mj-text>

          <mj-spacer height="12px" />

          <mj-text font-size="14px" color="#64748b" line-height="22px">
            1. Click the "Get Started" button above or visit our login page<br/>
            2. Enter your email address and the temporary password provided<br/>
            3. Follow the prompts to set up your new password<br/>
            4. Complete your profile setup
          </mj-text>

          <mj-spacer height="32px" />

          <!-- Security Notice -->
          <mj-section background-color="#fef3c7" border-radius="8px" padding="20px">
            <mj-column>
              <mj-text font-size="14px" color="#92400e" line-height="20px">
                <strong>🔒 Security Notice:</strong> This temporary password will expire in 24 hours. Please log in and change your password as soon as possible.
              </mj-text>
            </mj-column>
          </mj-section>

          <mj-spacer height="40px" />
        </mj-column>
      </mj-section>

      @include('emails/components/support')

      @include('emails/components/footer')
    </mj-body>
  </mjml>
@end