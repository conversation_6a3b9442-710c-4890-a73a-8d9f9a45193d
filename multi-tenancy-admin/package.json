{"name": "multi-tenancy-admin", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev --port 4444", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/fs-routes": "^7.6.2", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-query": "^5.80.7", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.27", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-router": "^7.5.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.74", "zustand": "^5.0.6"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-devtools-json": "^0.2.0", "vite-tsconfig-paths": "^5.1.4"}}