import React, { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Plus, UserPlus } from "lucide-react";
import { useUsers, useDeleteUser } from "~/lib/hooks/useUsers";
import type { User as UserType, UserStatus } from "~/types";
import { UserStatus as UserStatusEnum } from "~/types";
import { DataTable } from "~/components/ui/data-table";
import { TableCells } from "~/components/ui/table-cells";
import {
  EmptyStates,
  createEmptyStateAction,
} from "~/components/ui/empty-state";
import type { ColumnDef } from "~/types/data-table";
import { TenantLink } from "~/components/tenant-link";
import { RESOURCES, usePermissions } from "~/lib/hooks/usePermissions";

const getUserStatusBadge = (status: UserStatus) => {
  switch (status) {
    case UserStatusEnum.ACTIVE:
      return <Badge variant="default">Active</Badge>;
    case UserStatusEnum.INACTIVE:
      return <Badge variant="secondary">Inactive</Badge>;
    case UserStatusEnum.SUSPENDED:
      return <Badge variant="destructive">Suspended</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};

const Users = () => {
  const [search, setSearch] = useState("");

  // Queries
  const { data: usersResponse, isLoading, error } = useUsers({ search });
  const users = usersResponse?.data?.data || [];

  // Mutations
  const deleteUserMutation = useDeleteUser();

  const handleDeleteUser = async (id: string) => {
    try {
      await deleteUserMutation.mutateAsync(id);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  // Define columns for the DataTable
  const columns: ColumnDef<UserType>[] = [
    {
      id: "user",
      header: "User",
      cell: (user) => (
        <div className="min-w-0">
          <TableCells.User name={user.fullName} />
        </div>
      ),
    },
    {
      id: "email",
      header: "Email",
      cell: (user) => <TableCells.Text>{user.email}</TableCells.Text>,
    },
    {
      id: "phone",
      header: "Phone",
      cell: (user) => <TableCells.Text>{user.phone || "N/A"}</TableCells.Text>,
    },
    {
      id: "status",
      header: "Status",
      cell: (user) => getUserStatusBadge(user.status),
    },
    {
      id: "roles",
      header: "Roles",
      cell: (user) => {
        if (!user.roles || user.roles.length === 0) {
          return <TableCells.Text>No roles</TableCells.Text>;
        }

        return (
          <div className="flex flex-wrap gap-1">
            {user.roles.map((role, index) => (
              <Badge
                key={role.id || index}
                variant="secondary"
                className="text-xs"
              >
                {role.displayName || role.name}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      id: "created",
      header: "Created",
      cell: (user) => <TableCells.Date date={user.createdAt} />,
    },
    {
      id: "actions",
      header: "Actions",
      className: "w-[100px]",
      cell: (user) => (
        <TableCells.Actions
          editHref={`/users/${user.id}`}
          onDelete={() => handleDeleteUser(user.id.toString())}
          deleteTitle="Delete User"
          deleteDescription={`Are you sure you want to remove "${user.fullName}" from this tenant? This action will remove their access to this tenant but will not delete their account.`}
          additionalActions={[]}
        />
      ),
    },
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <EmptyStates.Error
          title="Failed to load users"
          description="We encountered an error while loading the users. Please try again."
          action={createEmptyStateAction.retry(() => window.location.reload())}
        />
      </div>
    );
  }
  const { canAccess } = usePermissions();
  return (
    <div className="flex-1 space-y-4">
      {/* Custom Header with Multiple Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">
            Users
          </h2>
          <p className="text-muted-foreground text-sm sm:text-base">
            Manage users and their access
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          {canAccess(RESOURCES.USERS, "create") && (
            <TenantLink to="/users/invite">
              <Button variant="outline" className="w-full sm:w-auto">
                <UserPlus className="mr-2 h-4 w-4" />
                Invite User
              </Button>
            </TenantLink>
          )}
        </div>
      </div>

      {/* DataTable without title/description since we have custom header */}
      <DataTable
        data={users}
        isLoading={isLoading}
        columns={columns}
        searchValue={search}
        onSearchChange={setSearch}
        searchPlaceholder="Search users..."
        emptyMessage="No users found"
        searchEmptyMessage="No users found matching your search"
        getRowKey={(user) => user.id}
        tableKey="users"
        defaultColumns={[
          "user",
          "email",
          "phone",
          "status",
          "roles",
          "created",
        ]}
      />
    </div>
  );
};

export default Users;
